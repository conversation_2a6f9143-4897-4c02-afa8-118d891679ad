[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js": "16", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "19", "C:\\CMS\\webapp\\frontend\\src\\config.js": "20", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "21", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "29", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js": "30", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js": "31", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js": "32", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js": "33", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "34", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "35", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "36", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "37", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "42", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "43", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "44", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js": "47", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "49", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js": "50", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "51", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "52", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "55", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "56", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "57", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "58", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "59", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "60", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "61", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "62", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "63", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "64", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "65", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js": "69", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "70", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "71", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "72", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "73", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "74", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "75", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "76", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "80", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "81", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "88", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "89", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "90", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "91", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js": "92", "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js": "93", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js": "94", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js": "95", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js": "96", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCEI64_8Page.js": "97", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\CertificazioneCEI64_8.js": "98", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js": "99", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js": "100", "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js": "101", "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js": "102", "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js": "103", "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js": "104", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js": "105", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js": "106", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js": "107", "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js": "108", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js": "109", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js": "110"}, {"size": 557, "mtime": 1746952718482, "results": "111", "hashOfConfig": "112"}, {"size": 3196, "mtime": 1748982170834, "results": "113", "hashOfConfig": "112"}, {"size": 996, "mtime": 1746970152489, "results": "114", "hashOfConfig": "112"}, {"size": 10788, "mtime": 1746864244183, "results": "115", "hashOfConfig": "112"}, {"size": 21191, "mtime": 1748751093271, "results": "116", "hashOfConfig": "112"}, {"size": 7019, "mtime": 1748984331356, "results": "117", "hashOfConfig": "112"}, {"size": 2216, "mtime": 1746640055487, "results": "118", "hashOfConfig": "112"}, {"size": 7394, "mtime": 1748034003517, "results": "119", "hashOfConfig": "112"}, {"size": 6749, "mtime": 1746282201800, "results": "120", "hashOfConfig": "112"}, {"size": 18682, "mtime": 1748984732119, "results": "121", "hashOfConfig": "112"}, {"size": 2535, "mtime": 1746647873596, "results": "122", "hashOfConfig": "112"}, {"size": 2050, "mtime": 1746647945415, "results": "123", "hashOfConfig": "112"}, {"size": 700, "mtime": 1747545501078, "results": "124", "hashOfConfig": "112"}, {"size": 17518, "mtime": 1748664526035, "results": "125", "hashOfConfig": "112"}, {"size": 3028, "mtime": 1748816305304, "results": "126", "hashOfConfig": "112"}, {"size": 2070, "mtime": 1748815989656, "results": "127", "hashOfConfig": "112"}, {"size": 1630, "mtime": 1746336079554, "results": "128", "hashOfConfig": "112"}, {"size": 1909, "mtime": 1748722592098, "results": "129", "hashOfConfig": "112"}, {"size": 57960, "mtime": 1748985002845, "results": "130", "hashOfConfig": "112"}, {"size": 324, "mtime": 1748757444974, "results": "131", "hashOfConfig": "112"}, {"size": 9068, "mtime": 1746856425683, "results": "132", "hashOfConfig": "112"}, {"size": 2210, "mtime": 1747432283057, "results": "133", "hashOfConfig": "112"}, {"size": 4494, "mtime": 1748121063631, "results": "134", "hashOfConfig": "112"}, {"size": 38195, "mtime": 1748813903832, "results": "135", "hashOfConfig": "112"}, {"size": 3337, "mtime": 1748816346924, "results": "136", "hashOfConfig": "112"}, {"size": 2958, "mtime": 1748816316425, "results": "137", "hashOfConfig": "112"}, {"size": 3507, "mtime": 1748816326922, "results": "138", "hashOfConfig": "112"}, {"size": 3345, "mtime": 1748816357091, "results": "139", "hashOfConfig": "112"}, {"size": 3340, "mtime": 1748816336281, "results": "140", "hashOfConfig": "112"}, {"size": 2975, "mtime": 1747554796402, "results": "141", "hashOfConfig": "112"}, {"size": 3429, "mtime": 1747721794176, "results": "142", "hashOfConfig": "112"}, {"size": 3109, "mtime": 1747824114392, "results": "143", "hashOfConfig": "112"}, {"size": 2929, "mtime": 1747655572696, "results": "144", "hashOfConfig": "112"}, {"size": 6125, "mtime": 1748705680231, "results": "145", "hashOfConfig": "112"}, {"size": 5880, "mtime": 1748121404574, "results": "146", "hashOfConfig": "112"}, {"size": 3889, "mtime": 1748664890350, "results": "147", "hashOfConfig": "112"}, {"size": 4720, "mtime": 1746771178920, "results": "148", "hashOfConfig": "112"}, {"size": 7121, "mtime": 1746281148395, "results": "149", "hashOfConfig": "112"}, {"size": 7958, "mtime": 1746280443400, "results": "150", "hashOfConfig": "112"}, {"size": 6259, "mtime": 1746965906057, "results": "151", "hashOfConfig": "112"}, {"size": 4215, "mtime": 1746278746358, "results": "152", "hashOfConfig": "112"}, {"size": 1273, "mtime": 1746809069006, "results": "153", "hashOfConfig": "112"}, {"size": 14270, "mtime": 1748371983481, "results": "154", "hashOfConfig": "112"}, {"size": 2752, "mtime": 1747022186740, "results": "155", "hashOfConfig": "112"}, {"size": 1072, "mtime": 1746637929350, "results": "156", "hashOfConfig": "112"}, {"size": 6745, "mtime": 1747545492454, "results": "157", "hashOfConfig": "112"}, {"size": 41680, "mtime": 1748816669877, "results": "158", "hashOfConfig": "112"}, {"size": 500, "mtime": 1748722841235, "results": "159", "hashOfConfig": "112"}, {"size": 47844, "mtime": 1748876421138, "results": "160", "hashOfConfig": "112"}, {"size": 38126, "mtime": 1748984291159, "results": "161", "hashOfConfig": "112"}, {"size": 1947, "mtime": 1748120984640, "results": "162", "hashOfConfig": "112"}, {"size": 54895, "mtime": 1748370360136, "results": "163", "hashOfConfig": "112"}, {"size": 14635, "mtime": 1748666301849, "results": "164", "hashOfConfig": "112"}, {"size": 15230, "mtime": 1748984833955, "results": "165", "hashOfConfig": "112"}, {"size": 11835, "mtime": 1748920731807, "results": "166", "hashOfConfig": "112"}, {"size": 2211, "mtime": 1748686293878, "results": "167", "hashOfConfig": "112"}, {"size": 9215, "mtime": 1748668814050, "results": "168", "hashOfConfig": "112"}, {"size": 10993, "mtime": 1747154871546, "results": "169", "hashOfConfig": "112"}, {"size": 12150, "mtime": 1748205557322, "results": "170", "hashOfConfig": "112"}, {"size": 24566, "mtime": 1748691444876, "results": "171", "hashOfConfig": "112"}, {"size": 7032, "mtime": 1748069273238, "results": "172", "hashOfConfig": "112"}, {"size": 8589, "mtime": 1748207111023, "results": "173", "hashOfConfig": "112"}, {"size": 9979, "mtime": 1748069243848, "results": "174", "hashOfConfig": "112"}, {"size": 10821, "mtime": 1748069202177, "results": "175", "hashOfConfig": "112"}, {"size": 36555, "mtime": 1747684003188, "results": "176", "hashOfConfig": "112"}, {"size": 9483, "mtime": 1747194869458, "results": "177", "hashOfConfig": "112"}, {"size": 20387, "mtime": 1748984521895, "results": "178", "hashOfConfig": "112"}, {"size": 48588, "mtime": 1747948123233, "results": "179", "hashOfConfig": "112"}, {"size": 92270, "mtime": 1748123070273, "results": "180", "hashOfConfig": "112"}, {"size": 522, "mtime": 1747022186711, "results": "181", "hashOfConfig": "112"}, {"size": 10251, "mtime": 1748805459799, "results": "182", "hashOfConfig": "112"}, {"size": 7740, "mtime": 1748881233022, "results": "183", "hashOfConfig": "112"}, {"size": 1703, "mtime": 1746972529152, "results": "184", "hashOfConfig": "112"}, {"size": 19892, "mtime": 1747554544219, "results": "185", "hashOfConfig": "112"}, {"size": 12050, "mtime": 1747547543421, "results": "186", "hashOfConfig": "112"}, {"size": 1686, "mtime": 1746946499500, "results": "187", "hashOfConfig": "112"}, {"size": 5145, "mtime": 1746914029633, "results": "188", "hashOfConfig": "112"}, {"size": 10721, "mtime": 1748751269815, "results": "189", "hashOfConfig": "112"}, {"size": 22179, "mtime": 1747432554979, "results": "190", "hashOfConfig": "112"}, {"size": 2574, "mtime": 1748920719208, "results": "191", "hashOfConfig": "112"}, {"size": 4094, "mtime": 1748161663641, "results": "192", "hashOfConfig": "112"}, {"size": 5273, "mtime": 1747946737459, "results": "193", "hashOfConfig": "112"}, {"size": 4346, "mtime": 1747491472989, "results": "194", "hashOfConfig": "112"}, {"size": 15647, "mtime": 1748899398456, "results": "195", "hashOfConfig": "112"}, {"size": 6742, "mtime": 1748751174061, "results": "196", "hashOfConfig": "112"}, {"size": 6529, "mtime": 1748664406267, "results": "197", "hashOfConfig": "112"}, {"size": 15764, "mtime": 1748877145346, "results": "198", "hashOfConfig": "112"}, {"size": 6899, "mtime": 1748877131332, "results": "199", "hashOfConfig": "112"}, {"size": 5536, "mtime": 1748670096009, "results": "200", "hashOfConfig": "112"}, {"size": 5457, "mtime": 1748666884369, "results": "201", "hashOfConfig": "112"}, {"size": 5605, "mtime": 1748666925194, "results": "202", "hashOfConfig": "112"}, {"size": 77752, "mtime": 1748878387989, "results": "203", "hashOfConfig": "112"}, {"size": 2807, "mtime": 1748705699971, "results": "204", "hashOfConfig": "112"}, {"size": 23591, "mtime": 1748881382254, "results": "205", "hashOfConfig": "112"}, {"size": 3708, "mtime": 1748705727900, "results": "206", "hashOfConfig": "112"}, {"size": 10270, "mtime": 1748724524628, "results": "207", "hashOfConfig": "112"}, {"size": 8247, "mtime": 1748756088995, "results": "208", "hashOfConfig": "112"}, {"size": 11038, "mtime": 1748756003708, "results": "209", "hashOfConfig": "112"}, {"size": 15055, "mtime": 1748755908778, "results": "210", "hashOfConfig": "112"}, {"size": 16415, "mtime": 1748755956687, "results": "211", "hashOfConfig": "112"}, {"size": 3434, "mtime": 1748755857115, "results": "212", "hashOfConfig": "112"}, {"size": 3483, "mtime": 1748755829302, "results": "213", "hashOfConfig": "112"}, {"size": 3508, "mtime": 1748755842942, "results": "214", "hashOfConfig": "112"}, {"size": 956, "mtime": 1748878396989, "results": "215", "hashOfConfig": "112"}, {"size": 13327, "mtime": 1748881322351, "results": "216", "hashOfConfig": "112"}, {"size": 16151, "mtime": 1748981113532, "results": "217", "hashOfConfig": "112"}, {"size": 3613, "mtime": 1748921268108, "results": "218", "hashOfConfig": "112"}, {"size": 1153, "mtime": 1748921279608, "results": "219", "hashOfConfig": "112"}, {"size": 6579, "mtime": 1748922219011, "results": "220", "hashOfConfig": "112"}, {"size": 8976, "mtime": 1748922249445, "results": "221", "hashOfConfig": "112"}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", ["552"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", ["553", "554", "555", "556", "557"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["558", "559", "560", "561"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["562", "563", "564", "565", "566", "567", "568", "569", "570", "571"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["572"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["573", "574", "575", "576", "577", "578", "579", "580", "581", "582"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["598"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["599", "600", "601", "602", "603", "604", "605", "606"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["628"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["629"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["630", "631", "632"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["633", "634", "635", "636"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", ["637", "638"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", ["639", "640"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js", ["641", "642", "643", "644"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["645", "646", "647", "648"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js", ["649"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js", ["650", "651", "652", "653", "654", "655", "656"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js", ["657", "658", "659"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["660", "661"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["662", "663"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["664", "665"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["666", "667"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js", ["668", "669", "670", "671", "672", "673", "674", "675"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js", ["690", "691", "692", "693", "694", "695", "696"], ["697"], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["721", "722"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", ["723", "724", "725", "726", "727", "728", "729", "730"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["731", "732"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["744"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["767"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["768"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["769"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["781"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js", ["796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js", ["820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["836", "837"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["838"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["839", "840"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["861", "862", "863", "864", "865", "866", "867", "868"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["869"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["870", "871", "872"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["873", "874"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["875"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", ["876"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["877"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["878"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js", ["879", "880", "881", "882", "883", "884"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js", ["885"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCEI64_8Page.js", ["886", "887", "888", "889", "890", "891", "892", "893"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\CertificazioneCEI64_8.js", ["894", "895", "896", "897", "898", "899", "900"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js", ["901", "902", "903", "904", "905", "906", "907", "908", "909"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js", ["910", "911", "912", "913", "914", "915", "916", "917", "918"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js", ["919", "920", "921"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js", ["922", "923", "924"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js", ["925", "926", "927", "928"], [], {"ruleId": "929", "severity": 1, "message": "930", "line": 12, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 12, "endColumn": 14}, {"ruleId": "933", "severity": 1, "message": "934", "line": 96, "column": 71, "nodeType": "935", "messageId": "936", "endLine": 96, "endColumn": 100}, {"ruleId": "933", "severity": 1, "message": "934", "line": 97, "column": 70, "nodeType": "935", "messageId": "936", "endLine": 97, "endColumn": 99}, {"ruleId": "933", "severity": 1, "message": "934", "line": 98, "column": 67, "nodeType": "935", "messageId": "936", "endLine": 98, "endColumn": 96}, {"ruleId": "933", "severity": 1, "message": "934", "line": 99, "column": 76, "nodeType": "935", "messageId": "936", "endLine": 99, "endColumn": 105}, {"ruleId": "933", "severity": 1, "message": "934", "line": 100, "column": 71, "nodeType": "935", "messageId": "936", "endLine": 100, "endColumn": 100}, {"ruleId": "937", "severity": 1, "message": "938", "line": 78, "column": 11, "nodeType": "939", "messageId": "940", "endLine": 78, "endColumn": 115}, {"ruleId": "937", "severity": 1, "message": "938", "line": 80, "column": 11, "nodeType": "939", "messageId": "940", "endLine": 80, "endColumn": 107}, {"ruleId": "937", "severity": 1, "message": "938", "line": 86, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 86, "endColumn": 105}, {"ruleId": "937", "severity": 1, "message": "938", "line": 89, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 89, "endColumn": 41}, {"ruleId": "929", "severity": 1, "message": "941", "line": 13, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 13, "endColumn": 9}, {"ruleId": "929", "severity": 1, "message": "942", "line": 20, "column": 25, "nodeType": "931", "messageId": "932", "endLine": 20, "endColumn": 34}, {"ruleId": "929", "severity": 1, "message": "943", "line": 21, "column": 19, "nodeType": "931", "messageId": "932", "endLine": 21, "endColumn": 35}, {"ruleId": "929", "severity": 1, "message": "944", "line": 22, "column": 12, "nodeType": "931", "messageId": "932", "endLine": 22, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "945", "line": 23, "column": 18, "nodeType": "931", "messageId": "932", "endLine": 23, "endColumn": 28}, {"ruleId": "929", "severity": 1, "message": "946", "line": 57, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 57, "endColumn": 22}, {"ruleId": "929", "severity": 1, "message": "947", "line": 58, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 58, "endColumn": 23}, {"ruleId": "929", "severity": 1, "message": "948", "line": 59, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 59, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "949", "line": 60, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 60, "endColumn": 22}, {"ruleId": "929", "severity": 1, "message": "950", "line": 69, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 69, "endColumn": 29}, {"ruleId": "929", "severity": 1, "message": "951", "line": 1, "column": 8, "nodeType": "931", "messageId": "932", "endLine": 1, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "952", "line": 2, "column": 27, "nodeType": "931", "messageId": "932", "endLine": 2, "endColumn": 31}, {"ruleId": "929", "severity": 1, "message": "953", "line": 2, "column": 33, "nodeType": "931", "messageId": "932", "endLine": 2, "endColumn": 37}, {"ruleId": "929", "severity": 1, "message": "954", "line": 2, "column": 39, "nodeType": "931", "messageId": "932", "endLine": 2, "endColumn": 50}, {"ruleId": "929", "severity": 1, "message": "955", "line": 2, "column": 52, "nodeType": "931", "messageId": "932", "endLine": 2, "endColumn": 66}, {"ruleId": "929", "severity": 1, "message": "941", "line": 2, "column": 68, "nodeType": "931", "messageId": "932", "endLine": 2, "endColumn": 74}, {"ruleId": "929", "severity": 1, "message": "942", "line": 5, "column": 25, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 34}, {"ruleId": "929", "severity": 1, "message": "943", "line": 6, "column": 19, "nodeType": "931", "messageId": "932", "endLine": 6, "endColumn": 35}, {"ruleId": "929", "severity": 1, "message": "944", "line": 7, "column": 12, "nodeType": "931", "messageId": "932", "endLine": 7, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "945", "line": 8, "column": 18, "nodeType": "931", "messageId": "932", "endLine": 8, "endColumn": 28}, {"ruleId": "929", "severity": 1, "message": "956", "line": 43, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 43, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "953", "line": 8, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 8, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "954", "line": 9, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 9, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "930", "line": 10, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 10, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "952", "line": 11, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 11, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "957", "line": 12, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 12, "endColumn": 10}, {"ruleId": "929", "severity": 1, "message": "958", "line": 15, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 15, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "959", "line": 16, "column": 15, "nodeType": "931", "messageId": "932", "endLine": 16, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "960", "line": 17, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 17, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "961", "line": 18, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 18, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "962", "line": 19, "column": 13, "nodeType": "931", "messageId": "932", "endLine": 19, "endColumn": 23}, {"ruleId": "929", "severity": 1, "message": "963", "line": 20, "column": 14, "nodeType": "931", "messageId": "932", "endLine": 20, "endColumn": 25}, {"ruleId": "929", "severity": 1, "message": "964", "line": 25, "column": 8, "nodeType": "931", "messageId": "932", "endLine": 25, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "965", "line": 28, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 28, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "966", "line": 48, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 48, "endColumn": 22}, {"ruleId": "929", "severity": 1, "message": "967", "line": 53, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 53, "endColumn": 20}, {"ruleId": "929", "severity": 1, "message": "968", "line": 11, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 11, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "969", "line": 4, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 4, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "970", "line": 5, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 8}, {"ruleId": "929", "severity": 1, "message": "971", "line": 7, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 7, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "972", "line": 12, "column": 14, "nodeType": "931", "messageId": "932", "endLine": 12, "endColumn": 25}, {"ruleId": "929", "severity": 1, "message": "958", "line": 13, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 13, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "973", "line": 17, "column": 8, "nodeType": "931", "messageId": "932", "endLine": 17, "endColumn": 23}, {"ruleId": "929", "severity": 1, "message": "965", "line": 21, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 21, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "974", "line": 26, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 26, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "953", "line": 8, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 8, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "954", "line": 9, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 9, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "971", "line": 11, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 11, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "975", "line": 14, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 14, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "976", "line": 26, "column": 8, "nodeType": "931", "messageId": "932", "endLine": 26, "endColumn": 16}, {"ruleId": "929", "severity": 1, "message": "977", "line": 30, "column": 15, "nodeType": "931", "messageId": "932", "endLine": 30, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "978", "line": 32, "column": 14, "nodeType": "931", "messageId": "932", "endLine": 32, "endColumn": 25}, {"ruleId": "929", "severity": 1, "message": "979", "line": 33, "column": 15, "nodeType": "931", "messageId": "932", "endLine": 33, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "980", "line": 34, "column": 15, "nodeType": "931", "messageId": "932", "endLine": 34, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "981", "line": 35, "column": 27, "nodeType": "931", "messageId": "932", "endLine": 35, "endColumn": 51}, {"ruleId": "929", "severity": 1, "message": "982", "line": 41, "column": 15, "nodeType": "931", "messageId": "932", "endLine": 41, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "983", "line": 50, "column": 8, "nodeType": "931", "messageId": "932", "endLine": 50, "endColumn": 16}, {"ruleId": "929", "severity": 1, "message": "965", "line": 59, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 59, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "984", "line": 61, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 61, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "974", "line": 63, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 63, "endColumn": 22}, {"ruleId": "929", "severity": 1, "message": "985", "line": 248, "column": 19, "nodeType": "931", "messageId": "932", "endLine": 248, "endColumn": 29}, {"ruleId": "929", "severity": 1, "message": "986", "line": 256, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 256, "endColumn": 28}, {"ruleId": "929", "severity": 1, "message": "987", "line": 257, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 257, "endColumn": 23}, {"ruleId": "929", "severity": 1, "message": "988", "line": 257, "column": 25, "nodeType": "931", "messageId": "932", "endLine": 257, "endColumn": 41}, {"ruleId": "989", "severity": 1, "message": "990", "line": 617, "column": 6, "nodeType": "991", "endLine": 617, "endColumn": 15, "suggestions": "992"}, {"ruleId": "929", "severity": 1, "message": "993", "line": 662, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 662, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "994", "line": 1, "column": 27, "nodeType": "931", "messageId": "932", "endLine": 1, "endColumn": 36}, {"ruleId": "929", "severity": 1, "message": "995", "line": 49, "column": 19, "nodeType": "931", "messageId": "932", "endLine": 49, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "957", "line": 15, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 15, "endColumn": 10}, {"ruleId": "929", "severity": 1, "message": "972", "line": 39, "column": 14, "nodeType": "931", "messageId": "932", "endLine": 39, "endColumn": 25}, {"ruleId": "929", "severity": 1, "message": "996", "line": 43, "column": 16, "nodeType": "931", "messageId": "932", "endLine": 43, "endColumn": 29}, {"ruleId": "929", "severity": 1, "message": "969", "line": 4, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 4, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "970", "line": 5, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 8}, {"ruleId": "929", "severity": 1, "message": "974", "line": 26, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 26, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "997", "line": 48, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 48, "endColumn": 29}, {"ruleId": "929", "severity": 1, "message": "969", "line": 4, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 4, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "997", "line": 37, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 37, "endColumn": 29}, {"ruleId": "929", "severity": 1, "message": "969", "line": 4, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 4, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "997", "line": 52, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 52, "endColumn": 29}, {"ruleId": "929", "severity": 1, "message": "969", "line": 4, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 4, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "970", "line": 5, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 8}, {"ruleId": "929", "severity": 1, "message": "974", "line": 26, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 26, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "997", "line": 48, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 48, "endColumn": 29}, {"ruleId": "929", "severity": 1, "message": "969", "line": 4, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 4, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "970", "line": 5, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 8}, {"ruleId": "929", "severity": 1, "message": "974", "line": 26, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 26, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "997", "line": 48, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 48, "endColumn": 29}, {"ruleId": "929", "severity": 1, "message": "974", "line": 27, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 27, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "970", "line": 5, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 8}, {"ruleId": "929", "severity": 1, "message": "998", "line": 6, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 6, "endColumn": 9}, {"ruleId": "929", "severity": 1, "message": "958", "line": 14, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 14, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "965", "line": 23, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 23, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "974", "line": 30, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 30, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "997", "line": 33, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 33, "endColumn": 29}, {"ruleId": "929", "severity": 1, "message": "999", "line": 38, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 38, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "965", "line": 20, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 20, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "974", "line": 27, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 27, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "999", "line": 35, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 35, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "965", "line": 24, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 24, "endColumn": 26}, {"ruleId": "989", "severity": 1, "message": "1000", "line": 53, "column": 6, "nodeType": "991", "endLine": 53, "endColumn": 18, "suggestions": "1001"}, {"ruleId": "929", "severity": 1, "message": "1002", "line": 1, "column": 8, "nodeType": "931", "messageId": "932", "endLine": 1, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "1003", "line": 5, "column": 7, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "957", "line": 14, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 14, "endColumn": 10}, {"ruleId": "929", "severity": 1, "message": "1004", "line": 28, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 28, "endColumn": 18}, {"ruleId": "929", "severity": 1, "message": "1002", "line": 1, "column": 8, "nodeType": "931", "messageId": "932", "endLine": 1, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "1003", "line": 5, "column": 7, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "953", "line": 8, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 8, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "954", "line": 9, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 9, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "930", "line": 10, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 10, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1005", "line": 23, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 23, "endColumn": 15}, {"ruleId": "929", "severity": 1, "message": "1006", "line": 24, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 24, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "959", "line": 46, "column": 15, "nodeType": "931", "messageId": "932", "endLine": 46, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "1007", "line": 47, "column": 12, "nodeType": "931", "messageId": "932", "endLine": 47, "endColumn": 21}, {"ruleId": "989", "severity": 1, "message": "1008", "line": 134, "column": 6, "nodeType": "991", "endLine": 134, "endColumn": 18, "suggestions": "1009"}, {"ruleId": "929", "severity": 1, "message": "953", "line": 8, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 8, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "954", "line": 9, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 9, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "930", "line": 10, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 10, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1005", "line": 23, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 23, "endColumn": 15}, {"ruleId": "929", "severity": 1, "message": "1006", "line": 24, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 24, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "957", "line": 25, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 25, "endColumn": 10}, {"ruleId": "929", "severity": 1, "message": "971", "line": 29, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 29, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "961", "line": 39, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 39, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "959", "line": 43, "column": 15, "nodeType": "931", "messageId": "932", "endLine": 43, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "1010", "line": 44, "column": 14, "nodeType": "931", "messageId": "932", "endLine": 44, "endColumn": 25}, {"ruleId": "929", "severity": 1, "message": "1011", "line": 50, "column": 69, "nodeType": "931", "messageId": "932", "endLine": 50, "endColumn": 76}, {"ruleId": "929", "severity": 1, "message": "1012", "line": 79, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 79, "endColumn": 26}, {"ruleId": "989", "severity": 1, "message": "1013", "line": 179, "column": 6, "nodeType": "991", "endLine": 179, "endColumn": 8, "suggestions": "1014"}, {"ruleId": "929", "severity": 1, "message": "1015", "line": 697, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 697, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "1016", "line": 20, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 20, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "1017", "line": 21, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 21, "endColumn": 9}, {"ruleId": "929", "severity": 1, "message": "1018", "line": 22, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 22, "endColumn": 11}, {"ruleId": "929", "severity": 1, "message": "952", "line": 23, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 23, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "1019", "line": 26, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 26, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "1020", "line": 69, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 69, "endColumn": 22}, {"ruleId": "1021", "severity": 1, "message": "1022", "line": 463, "column": 9, "nodeType": "1023", "messageId": "1024", "endLine": 466, "endColumn": 10}, {"ruleId": "989", "severity": 1, "message": "1025", "line": 95, "column": 6, "nodeType": "991", "endLine": 95, "endColumn": 21, "suggestions": "1026", "suppressions": "1027"}, {"ruleId": "937", "severity": 1, "message": "938", "line": 260, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 264, "endColumn": 11}, {"ruleId": "937", "severity": 1, "message": "938", "line": 274, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 274, "endColumn": 70}, {"ruleId": "937", "severity": 1, "message": "938", "line": 278, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 278, "endColumn": 54}, {"ruleId": "937", "severity": 1, "message": "938", "line": 333, "column": 11, "nodeType": "939", "messageId": "940", "endLine": 338, "endColumn": 13}, {"ruleId": "937", "severity": 1, "message": "938", "line": 435, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 439, "endColumn": 11}, {"ruleId": "937", "severity": 1, "message": "938", "line": 451, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 451, "endColumn": 54}, {"ruleId": "937", "severity": 1, "message": "938", "line": 668, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 668, "endColumn": 163}, {"ruleId": "937", "severity": 1, "message": "938", "line": 677, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 677, "endColumn": 70}, {"ruleId": "937", "severity": 1, "message": "938", "line": 681, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 681, "endColumn": 54}, {"ruleId": "929", "severity": 1, "message": "1028", "line": 755, "column": 17, "nodeType": "931", "messageId": "932", "endLine": 755, "endColumn": 22}, {"ruleId": "937", "severity": 1, "message": "938", "line": 775, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 779, "endColumn": 11}, {"ruleId": "937", "severity": 1, "message": "938", "line": 794, "column": 11, "nodeType": "939", "messageId": "940", "endLine": 798, "endColumn": 13}, {"ruleId": "937", "severity": 1, "message": "938", "line": 801, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 804, "endColumn": 11}, {"ruleId": "937", "severity": 1, "message": "938", "line": 810, "column": 11, "nodeType": "939", "messageId": "940", "endLine": 814, "endColumn": 13}, {"ruleId": "937", "severity": 1, "message": "938", "line": 817, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 820, "endColumn": 11}, {"ruleId": "937", "severity": 1, "message": "938", "line": 885, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 889, "endColumn": 11}, {"ruleId": "1029", "severity": 1, "message": "1030", "line": 955, "column": 3, "nodeType": "1031", "messageId": "1032", "endLine": 955, "endColumn": 29}, {"ruleId": "1029", "severity": 1, "message": "1033", "line": 1143, "column": 3, "nodeType": "1031", "messageId": "1032", "endLine": 1143, "endColumn": 23}, {"ruleId": "1029", "severity": 1, "message": "1034", "line": 1238, "column": 3, "nodeType": "1031", "messageId": "1032", "endLine": 1238, "endColumn": 20}, {"ruleId": "937", "severity": 1, "message": "938", "line": 1287, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 1287, "endColumn": 163}, {"ruleId": "937", "severity": 1, "message": "938", "line": 1317, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 1317, "endColumn": 163}, {"ruleId": "937", "severity": 1, "message": "938", "line": 1370, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 1370, "endColumn": 163}, {"ruleId": "937", "severity": 1, "message": "938", "line": 1412, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 1412, "endColumn": 163}, {"ruleId": "929", "severity": 1, "message": "1035", "line": 6, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 6, "endColumn": 8}, {"ruleId": "929", "severity": 1, "message": "957", "line": 11, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 11, "endColumn": 10}, {"ruleId": "929", "severity": 1, "message": "969", "line": 2, "column": 15, "nodeType": "931", "messageId": "932", "endLine": 2, "endColumn": 25}, {"ruleId": "929", "severity": 1, "message": "998", "line": 2, "column": 64, "nodeType": "931", "messageId": "932", "endLine": 2, "endColumn": 70}, {"ruleId": "929", "severity": 1, "message": "980", "line": 4, "column": 15, "nodeType": "931", "messageId": "932", "endLine": 4, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "1036", "line": 5, "column": 12, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "1037", "line": 6, "column": 17, "nodeType": "931", "messageId": "932", "endLine": 6, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "982", "line": 7, "column": 15, "nodeType": "931", "messageId": "932", "endLine": 7, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "1038", "line": 8, "column": 16, "nodeType": "931", "messageId": "932", "endLine": 8, "endColumn": 25}, {"ruleId": "929", "severity": 1, "message": "1039", "line": 121, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 121, "endColumn": 29}, {"ruleId": "929", "severity": 1, "message": "1002", "line": 1, "column": 8, "nodeType": "931", "messageId": "932", "endLine": 1, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "1003", "line": 5, "column": 7, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1040", "line": 3, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 3, "endColumn": 11}, {"ruleId": "929", "severity": 1, "message": "1041", "line": 4, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 4, "endColumn": 6}, {"ruleId": "929", "severity": 1, "message": "1042", "line": 5, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "1043", "line": 6, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 6, "endColumn": 11}, {"ruleId": "929", "severity": 1, "message": "1044", "line": 7, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 7, "endColumn": 6}, {"ruleId": "929", "severity": 1, "message": "1045", "line": 12, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 12, "endColumn": 9}, {"ruleId": "929", "severity": 1, "message": "1046", "line": 36, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 36, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "1047", "line": 50, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 50, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "1048", "line": 64, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 64, "endColumn": 20}, {"ruleId": "929", "severity": 1, "message": "1049", "line": 88, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 88, "endColumn": 22}, {"ruleId": "929", "severity": 1, "message": "1050", "line": 104, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 104, "endColumn": 30}, {"ruleId": "929", "severity": 1, "message": "1051", "line": 3, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 3, "endColumn": 12}, {"ruleId": "929", "severity": 1, "message": "1043", "line": 3, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 3, "endColumn": 11}, {"ruleId": "929", "severity": 1, "message": "1044", "line": 4, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 4, "endColumn": 6}, {"ruleId": "929", "severity": 1, "message": "1052", "line": 5, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 8}, {"ruleId": "929", "severity": 1, "message": "1053", "line": 6, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 6, "endColumn": 8}, {"ruleId": "929", "severity": 1, "message": "1054", "line": 7, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 7, "endColumn": 16}, {"ruleId": "929", "severity": 1, "message": "1055", "line": 8, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 8, "endColumn": 10}, {"ruleId": "929", "severity": 1, "message": "1045", "line": 9, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 9, "endColumn": 9}, {"ruleId": "929", "severity": 1, "message": "1056", "line": 10, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 10, "endColumn": 22}, {"ruleId": "929", "severity": 1, "message": "1040", "line": 11, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 11, "endColumn": 11}, {"ruleId": "929", "severity": 1, "message": "1041", "line": 12, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 12, "endColumn": 6}, {"ruleId": "929", "severity": 1, "message": "1042", "line": 13, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 13, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "1057", "line": 14, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 14, "endColumn": 16}, {"ruleId": "929", "severity": 1, "message": "1058", "line": 15, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 15, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "1051", "line": 16, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 16, "endColumn": 12}, {"ruleId": "929", "severity": 1, "message": "1059", "line": 18, "column": 40, "nodeType": "931", "messageId": "932", "endLine": 18, "endColumn": 44}, {"ruleId": "929", "severity": 1, "message": "1060", "line": 47, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 47, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "1061", "line": 64, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 64, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "1062", "line": 71, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 71, "endColumn": 20}, {"ruleId": "929", "severity": 1, "message": "1049", "line": 79, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 79, "endColumn": 22}, {"ruleId": "929", "severity": 1, "message": "1050", "line": 95, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 95, "endColumn": 30}, {"ruleId": "929", "severity": 1, "message": "1063", "line": 299, "column": 27, "nodeType": "931", "messageId": "932", "endLine": 299, "endColumn": 37}, {"ruleId": "929", "severity": 1, "message": "1064", "line": 300, "column": 27, "nodeType": "931", "messageId": "932", "endLine": 300, "endColumn": 36}, {"ruleId": "929", "severity": 1, "message": "970", "line": 3, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 3, "endColumn": 8}, {"ruleId": "989", "severity": 1, "message": "1065", "line": 54, "column": 6, "nodeType": "991", "endLine": 54, "endColumn": 34, "suggestions": "1066"}, {"ruleId": "929", "severity": 1, "message": "1067", "line": 25, "column": 13, "nodeType": "931", "messageId": "932", "endLine": 25, "endColumn": 25}, {"ruleId": "929", "severity": 1, "message": "1068", "line": 33, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 33, "endColumn": 15}, {"ruleId": "929", "severity": 1, "message": "1069", "line": 34, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 34, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1070", "line": 35, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 35, "endColumn": 22}, {"ruleId": "929", "severity": 1, "message": "1071", "line": 36, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 36, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "1072", "line": 37, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 37, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "1073", "line": 41, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 41, "endColumn": 20}, {"ruleId": "929", "severity": 1, "message": "1074", "line": 43, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 43, "endColumn": 34}, {"ruleId": "929", "severity": 1, "message": "1075", "line": 69, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 69, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "1076", "line": 69, "column": 19, "nodeType": "931", "messageId": "932", "endLine": 69, "endColumn": 29}, {"ruleId": "989", "severity": 1, "message": "1077", "line": 88, "column": 6, "nodeType": "991", "endLine": 88, "endColumn": 18, "suggestions": "1078"}, {"ruleId": "989", "severity": 1, "message": "1079", "line": 448, "column": 6, "nodeType": "991", "endLine": 448, "endColumn": 28, "suggestions": "1080"}, {"ruleId": "929", "severity": 1, "message": "1081", "line": 4, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 4, "endColumn": 12}, {"ruleId": "929", "severity": 1, "message": "1082", "line": 8, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 8, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "1083", "line": 9, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 9, "endColumn": 11}, {"ruleId": "929", "severity": 1, "message": "1084", "line": 10, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 10, "endColumn": 15}, {"ruleId": "929", "severity": 1, "message": "1085", "line": 12, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 12, "endColumn": 9}, {"ruleId": "929", "severity": 1, "message": "1086", "line": 13, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 13, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1087", "line": 14, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 14, "endColumn": 16}, {"ruleId": "929", "severity": 1, "message": "1088", "line": 15, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 15, "endColumn": 16}, {"ruleId": "929", "severity": 1, "message": "1089", "line": 36, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 36, "endColumn": 30}, {"ruleId": "929", "severity": 1, "message": "1090", "line": 37, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 37, "endColumn": 20}, {"ruleId": "989", "severity": 1, "message": "1065", "line": 46, "column": 6, "nodeType": "991", "endLine": 46, "endColumn": 18, "suggestions": "1091"}, {"ruleId": "929", "severity": 1, "message": "1092", "line": 265, "column": 23, "nodeType": "931", "messageId": "932", "endLine": 265, "endColumn": 44}, {"ruleId": "929", "severity": 1, "message": "1093", "line": 266, "column": 23, "nodeType": "931", "messageId": "932", "endLine": 266, "endColumn": 42}, {"ruleId": "929", "severity": 1, "message": "1092", "line": 381, "column": 21, "nodeType": "931", "messageId": "932", "endLine": 381, "endColumn": 42}, {"ruleId": "929", "severity": 1, "message": "1093", "line": 382, "column": 21, "nodeType": "931", "messageId": "932", "endLine": 382, "endColumn": 40}, {"ruleId": "929", "severity": 1, "message": "1094", "line": 9, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 9, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1016", "line": 10, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 10, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "1017", "line": 11, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 11, "endColumn": 9}, {"ruleId": "929", "severity": 1, "message": "1018", "line": 12, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 12, "endColumn": 11}, {"ruleId": "929", "severity": 1, "message": "1084", "line": 33, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 33, "endColumn": 15}, {"ruleId": "929", "severity": 1, "message": "1095", "line": 35, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 35, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "1010", "line": 42, "column": 14, "nodeType": "931", "messageId": "932", "endLine": 42, "endColumn": 25}, {"ruleId": "929", "severity": 1, "message": "1068", "line": 52, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 52, "endColumn": 15}, {"ruleId": "929", "severity": 1, "message": "1069", "line": 53, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 53, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1070", "line": 54, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 54, "endColumn": 22}, {"ruleId": "929", "severity": 1, "message": "1071", "line": 55, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 55, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "1072", "line": 56, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 56, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "1096", "line": 57, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 57, "endColumn": 15}, {"ruleId": "929", "severity": 1, "message": "1097", "line": 58, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 58, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "1098", "line": 59, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 59, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "984", "line": 72, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 72, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "1099", "line": 79, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 79, "endColumn": 23}, {"ruleId": "929", "severity": 1, "message": "1100", "line": 79, "column": 25, "nodeType": "931", "messageId": "932", "endLine": 79, "endColumn": 41}, {"ruleId": "929", "severity": 1, "message": "1101", "line": 80, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 80, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "1102", "line": 85, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 85, "endColumn": 26}, {"ruleId": "989", "severity": 1, "message": "1065", "line": 105, "column": 6, "nodeType": "991", "endLine": 105, "endColumn": 18, "suggestions": "1103"}, {"ruleId": "989", "severity": 1, "message": "1104", "line": 112, "column": 6, "nodeType": "991", "endLine": 112, "endColumn": 20, "suggestions": "1105"}, {"ruleId": "989", "severity": 1, "message": "1106", "line": 127, "column": 6, "nodeType": "991", "endLine": 127, "endColumn": 34, "suggestions": "1107"}, {"ruleId": "929", "severity": 1, "message": "1108", "line": 283, "column": 13, "nodeType": "931", "messageId": "932", "endLine": 283, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "1019", "line": 17, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 17, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "1084", "line": 34, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 34, "endColumn": 15}, {"ruleId": "929", "severity": 1, "message": "1095", "line": 35, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 35, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "1109", "line": 39, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 39, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "1068", "line": 51, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 51, "endColumn": 15}, {"ruleId": "929", "severity": 1, "message": "1069", "line": 52, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 52, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1071", "line": 54, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 54, "endColumn": 21}, {"ruleId": "929", "severity": 1, "message": "1072", "line": 55, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 55, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "1074", "line": 62, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 62, "endColumn": 34}, {"ruleId": "929", "severity": 1, "message": "1110", "line": 105, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 105, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "1111", "line": 105, "column": 28, "nodeType": "931", "messageId": "932", "endLine": 105, "endColumn": 47}, {"ruleId": "989", "severity": 1, "message": "1104", "line": 145, "column": 6, "nodeType": "991", "endLine": 145, "endColumn": 18, "suggestions": "1112"}, {"ruleId": "929", "severity": 1, "message": "1113", "line": 701, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 701, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "1114", "line": 1311, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 1311, "endColumn": 28}, {"ruleId": "929", "severity": 1, "message": "1115", "line": 1316, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 1316, "endColumn": 30}, {"ruleId": "929", "severity": 1, "message": "1116", "line": 1883, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 1883, "endColumn": 23}, {"ruleId": "929", "severity": 1, "message": "1002", "line": 1, "column": 8, "nodeType": "931", "messageId": "932", "endLine": 1, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "1003", "line": 5, "column": 7, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1117", "line": 1, "column": 8, "nodeType": "931", "messageId": "932", "endLine": 1, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1002", "line": 1, "column": 8, "nodeType": "931", "messageId": "932", "endLine": 1, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "1003", "line": 5, "column": 7, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1002", "line": 1, "column": 8, "nodeType": "931", "messageId": "932", "endLine": 1, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "1003", "line": 5, "column": 7, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1118", "line": 83, "column": 13, "nodeType": "931", "messageId": "932", "endLine": 83, "endColumn": 21}, {"ruleId": "937", "severity": 1, "message": "938", "line": 109, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 109, "endColumn": 163}, {"ruleId": "937", "severity": 1, "message": "938", "line": 123, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 123, "endColumn": 70}, {"ruleId": "937", "severity": 1, "message": "938", "line": 127, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 127, "endColumn": 54}, {"ruleId": "937", "severity": 1, "message": "938", "line": 212, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 212, "endColumn": 163}, {"ruleId": "937", "severity": 1, "message": "938", "line": 226, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 226, "endColumn": 70}, {"ruleId": "937", "severity": 1, "message": "938", "line": 230, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 230, "endColumn": 54}, {"ruleId": "937", "severity": 1, "message": "938", "line": 271, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 271, "endColumn": 163}, {"ruleId": "937", "severity": 1, "message": "938", "line": 280, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 280, "endColumn": 70}, {"ruleId": "937", "severity": 1, "message": "938", "line": 284, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 284, "endColumn": 54}, {"ruleId": "937", "severity": 1, "message": "938", "line": 320, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 320, "endColumn": 70}, {"ruleId": "937", "severity": 1, "message": "938", "line": 324, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 324, "endColumn": 54}, {"ruleId": "937", "severity": 1, "message": "938", "line": 360, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 360, "endColumn": 163}, {"ruleId": "937", "severity": 1, "message": "938", "line": 369, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 369, "endColumn": 70}, {"ruleId": "937", "severity": 1, "message": "938", "line": 373, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 373, "endColumn": 54}, {"ruleId": "937", "severity": 1, "message": "938", "line": 450, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 450, "endColumn": 163}, {"ruleId": "937", "severity": 1, "message": "938", "line": 459, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 459, "endColumn": 70}, {"ruleId": "937", "severity": 1, "message": "938", "line": 463, "column": 9, "nodeType": "939", "messageId": "940", "endLine": 463, "endColumn": 54}, {"ruleId": "929", "severity": 1, "message": "1119", "line": 12, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 12, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "960", "line": 27, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 27, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "976", "line": 30, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 30, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "1070", "line": 34, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 34, "endColumn": 29}, {"ruleId": "929", "severity": 1, "message": "1075", "line": 49, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 49, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "1076", "line": 49, "column": 19, "nodeType": "931", "messageId": "932", "endLine": 49, "endColumn": 29}, {"ruleId": "989", "severity": 1, "message": "1065", "line": 64, "column": 6, "nodeType": "991", "endLine": 64, "endColumn": 32, "suggestions": "1120"}, {"ruleId": "929", "severity": 1, "message": "1121", "line": 270, "column": 17, "nodeType": "931", "messageId": "932", "endLine": 270, "endColumn": 23}, {"ruleId": "929", "severity": 1, "message": "1122", "line": 17, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 17, "endColumn": 8}, {"ruleId": "929", "severity": 1, "message": "1018", "line": 16, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 16, "endColumn": 11}, {"ruleId": "929", "severity": 1, "message": "1017", "line": 17, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 17, "endColumn": 9}, {"ruleId": "929", "severity": 1, "message": "1016", "line": 19, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 19, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "976", "line": 14, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 14, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "1123", "line": 43, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 43, "endColumn": 26}, {"ruleId": "929", "severity": 1, "message": "971", "line": 12, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 12, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "1124", "line": 33, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 33, "endColumn": 29}, {"ruleId": "929", "severity": 1, "message": "1125", "line": 3, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 3, "endColumn": 6}, {"ruleId": "929", "severity": 1, "message": "957", "line": 9, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 9, "endColumn": 10}, {"ruleId": "929", "severity": 1, "message": "1126", "line": 20, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 20, "endColumn": 19}, {"ruleId": "989", "severity": 1, "message": "1127", "line": 136, "column": 6, "nodeType": "991", "endLine": 136, "endColumn": 18, "suggestions": "1128"}, {"ruleId": "989", "severity": 1, "message": "1129", "line": 141, "column": 6, "nodeType": "991", "endLine": 141, "endColumn": 52, "suggestions": "1130"}, {"ruleId": "989", "severity": 1, "message": "1131", "line": 146, "column": 6, "nodeType": "991", "endLine": 146, "endColumn": 62, "suggestions": "1132"}, {"ruleId": "989", "severity": 1, "message": "1133", "line": 151, "column": 6, "nodeType": "991", "endLine": 151, "endColumn": 28, "suggestions": "1134"}, {"ruleId": "989", "severity": 1, "message": "1135", "line": 160, "column": 6, "nodeType": "991", "endLine": 160, "endColumn": 39, "suggestions": "1136"}, {"ruleId": "989", "severity": 1, "message": "1137", "line": 68, "column": 6, "nodeType": "991", "endLine": 68, "endColumn": 18, "suggestions": "1138"}, {"ruleId": "929", "severity": 1, "message": "953", "line": 8, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 8, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "954", "line": 9, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 9, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "998", "line": 10, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 10, "endColumn": 9}, {"ruleId": "929", "severity": 1, "message": "1083", "line": 12, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 12, "endColumn": 11}, {"ruleId": "929", "severity": 1, "message": "1139", "line": 21, "column": 17, "nodeType": "931", "messageId": "932", "endLine": 21, "endColumn": 31}, {"ruleId": "929", "severity": 1, "message": "945", "line": 24, "column": 17, "nodeType": "931", "messageId": "932", "endLine": 24, "endColumn": 27}, {"ruleId": "989", "severity": 1, "message": "1140", "line": 180, "column": 6, "nodeType": "991", "endLine": 180, "endColumn": 25, "suggestions": "1141"}, {"ruleId": "933", "severity": 1, "message": "1142", "line": 243, "column": 15, "nodeType": "935", "messageId": "936", "endLine": 248, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "970", "line": 5, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 8}, {"ruleId": "929", "severity": 1, "message": "1006", "line": 15, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 15, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "957", "line": 16, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 16, "endColumn": 10}, {"ruleId": "929", "severity": 1, "message": "961", "line": 28, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 28, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "962", "line": 29, "column": 13, "nodeType": "931", "messageId": "932", "endLine": 29, "endColumn": 23}, {"ruleId": "929", "severity": 1, "message": "1143", "line": 39, "column": 34, "nodeType": "931", "messageId": "932", "endLine": 39, "endColumn": 59}, {"ruleId": "929", "severity": 1, "message": "1075", "line": 41, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 41, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "994", "line": 1, "column": 27, "nodeType": "931", "messageId": "932", "endLine": 1, "endColumn": 36}, {"ruleId": "929", "severity": 1, "message": "953", "line": 10, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 10, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "954", "line": 11, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 11, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "969", "line": 12, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 12, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "1122", "line": 27, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 27, "endColumn": 8}, {"ruleId": "929", "severity": 1, "message": "960", "line": 30, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 30, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "1144", "line": 33, "column": 17, "nodeType": "931", "messageId": "932", "endLine": 33, "endColumn": 25}, {"ruleId": "929", "severity": 1, "message": "945", "line": 34, "column": 17, "nodeType": "931", "messageId": "932", "endLine": 34, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "972", "line": 35, "column": 14, "nodeType": "931", "messageId": "932", "endLine": 35, "endColumn": 25}, {"ruleId": "929", "severity": 1, "message": "953", "line": 10, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 10, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "954", "line": 11, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 11, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1122", "line": 27, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 27, "endColumn": 8}, {"ruleId": "929", "severity": 1, "message": "1145", "line": 28, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 28, "endColumn": 12}, {"ruleId": "929", "severity": 1, "message": "1146", "line": 29, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 29, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "1147", "line": 30, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 30, "endColumn": 19}, {"ruleId": "929", "severity": 1, "message": "960", "line": 34, "column": 10, "nodeType": "931", "messageId": "932", "endLine": 34, "endColumn": 17}, {"ruleId": "929", "severity": 1, "message": "1148", "line": 37, "column": 17, "nodeType": "931", "messageId": "932", "endLine": 37, "endColumn": 31}, {"ruleId": "989", "severity": 1, "message": "1149", "line": 98, "column": 6, "nodeType": "991", "endLine": 98, "endColumn": 24, "suggestions": "1150"}, {"ruleId": "929", "severity": 1, "message": "953", "line": 4, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 4, "endColumn": 7}, {"ruleId": "929", "severity": 1, "message": "954", "line": 5, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 5, "endColumn": 14}, {"ruleId": "989", "severity": 1, "message": "1151", "line": 66, "column": 6, "nodeType": "991", "endLine": 66, "endColumn": 25, "suggestions": "1152"}, {"ruleId": "929", "severity": 1, "message": "1153", "line": 196, "column": 9, "nodeType": "931", "messageId": "932", "endLine": 196, "endColumn": 27}, {"ruleId": "929", "severity": 1, "message": "1154", "line": 233, "column": 11, "nodeType": "931", "messageId": "932", "endLine": 233, "endColumn": 24}, {"ruleId": "989", "severity": 1, "message": "1155", "line": 389, "column": 6, "nodeType": "991", "endLine": 389, "endColumn": 58, "suggestions": "1156"}, {"ruleId": "929", "severity": 1, "message": "1094", "line": 15, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 15, "endColumn": 14}, {"ruleId": "929", "severity": 1, "message": "1016", "line": 16, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 16, "endColumn": 13}, {"ruleId": "929", "severity": 1, "message": "1017", "line": 17, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 17, "endColumn": 9}, {"ruleId": "929", "severity": 1, "message": "1018", "line": 18, "column": 3, "nodeType": "931", "messageId": "932", "endLine": 18, "endColumn": 11}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "react/jsx-pascal-case", "Imported JSX component CertificazioneCEI64_8Page must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Avatar' is defined but never used.", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'Typography' is defined but never used.", "'Paper' is defined but never used.", "'IconButton' is defined but never used.", "'RefreshIcon' is defined but never used.", "'AdminHomeButton' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'LinearProgress' is defined but never used.", "'InfoIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'LinkOffIcon' is defined but never used.", "'TimelineIcon' is defined but never used.", "'CheckBoxIcon' is defined but never used.", "'CheckBoxOutlineBlankIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'CavoForm' is defined but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'caviAttivi', 'caviSpare', 'error', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["1157"], "'getAllSelectedCavi' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "'InventoryIcon' is defined but never used.", "'handleBackToCantieri' is assigned a value but never used.", "'Button' is defined but never used.", "'handleBackToAdmin' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["1158"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'BuildIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array.", ["1159"], "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["1160"], "'renderBobineCards' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormHelperText' is defined but never used.", "'formWarnings' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "IfStatement", "unreachableCode", "React Hook React.useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1161"], ["1162"], "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "Duplicate key 'getRevisioneCorrente'.", "Duplicate key 'getCaviInstallati'.", "'Stack' is defined but never used.", "'ClearIcon' is defined but never used.", "'RulerIcon' is defined but never used.", "'StartIcon' is defined but never used.", "'handleClearSelection' is assigned a value but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1163"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["1164"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["1165"], "'TextField' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'internalSelectedCavo' is assigned a value but never used.", "'openDialog' is assigned a value but never used.", ["1166"], "'latoPartenzaCollegato' is assigned a value but never used.", "'latoArrivoCollegato' is assigned a value but never used.", "'FormControl' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "'searchResults' is assigned a value but never used.", "'setSearchResults' is assigned a value but never used.", "'showSearchResults' is assigned a value but never used.", "'compatibleBobine' is assigned a value but never used.", ["1167"], "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["1168"], "React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1169"], "'bobina' is assigned a value but never used.", "'SaveIcon' is defined but never used.", "'incompatibleReel' is assigned a value but never used.", "'setIncompatibleReel' is assigned a value but never used.", ["1170"], "'handleBack' is assigned a value but never used.", "'buildFullBobinaId' is assigned a value but never used.", "'hasSufficientMeters' is assigned a value but never used.", "'getStepContent' is assigned a value but never used.", "'config' is defined but never used.", "'sentData' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", ["1171"], "'result' is assigned a value but never used.", "'Alert' is defined but never used.", "'filteredCantieri' is assigned a value but never used.", "'currentHoldDuration' is assigned a value but never used.", "'Box' is defined but never used.", "'CircularProgress' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1172"], "React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array.", ["1173"], "React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array.", ["1174"], "React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array.", ["1175"], "React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array.", ["1176"], "React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array.", ["1177"], "'AssignmentIcon' is defined but never used.", "React Hook React.useEffect has a missing dependency: 'getInitialAction'. Either include it or remove the dependency array.", ["1178"], "Imported JSX component CertificazioneCEI64_8 must be in PascalCase or SCREAMING_SNAKE_CASE", "'setSelectedCertificazione' is assigned a value but never used.", "'ViewIcon' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProve'. Either include it or remove the dependency array.", ["1179"], "React Hook useEffect has a missing dependency: 'loadCaviDisponibili'. Either include it or remove the dependency array.", ["1180"], "'matchesNumericTerm' is assigned a value but never used.", "'isNumericTerm' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'cavoMatchesTerm'. Either include it or remove the dependency array.", ["1181"], {"desc": "1182", "fix": "1183"}, {"desc": "1184", "fix": "1185"}, {"desc": "1186", "fix": "1187"}, {"desc": "1188", "fix": "1189"}, {"desc": "1190", "fix": "1191"}, {"kind": "1192", "justification": "1193"}, {"desc": "1194", "fix": "1195"}, {"desc": "1196", "fix": "1197"}, {"desc": "1198", "fix": "1199"}, {"desc": "1200", "fix": "1201"}, {"desc": "1200", "fix": "1202"}, {"desc": "1203", "fix": "1204"}, {"desc": "1205", "fix": "1206"}, {"desc": "1207", "fix": "1208"}, {"desc": "1209", "fix": "1210"}, {"desc": "1211", "fix": "1212"}, {"desc": "1213", "fix": "1214"}, {"desc": "1215", "fix": "1216"}, {"desc": "1217", "fix": "1218"}, {"desc": "1219", "fix": "1220"}, {"desc": "1221", "fix": "1222"}, {"desc": "1223", "fix": "1224"}, {"desc": "1225", "fix": "1226"}, {"desc": "1227", "fix": "1228"}, {"desc": "1229", "fix": "1230"}, "Update the dependencies array to be: [caviAttivi, caviSpare, error, filters, user]", {"range": "1231", "text": "1232"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1233", "text": "1234"}, "Update the dependencies array to be: [cantiereId, loadCertificazioni]", {"range": "1235", "text": "1236"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1237", "text": "1238"}, "Update the dependencies array to be: [initialOption, loadCavi]", {"range": "1239", "text": "1240"}, "directive", "", "Update the dependencies array to be: [certificazione, cantiereId, loadCavi]", {"range": "1241", "text": "1242"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1243", "text": "1244"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1245", "text": "1246"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1247", "text": "1248"}, {"range": "1249", "text": "1248"}, "Update the dependencies array to be: [loadBobine, selectedCavo]", {"range": "1250", "text": "1251"}, "Update the dependencies array to be: [cavoId, cavi, selectedCavo, onError]", {"range": "1252", "text": "1253"}, "Update the dependencies array to be: [cantiereId, loadBobine]", {"range": "1254", "text": "1255"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1256", "text": "1257"}, "Update the dependencies array to be: [cantiereId, loadInitialData]", {"range": "1258", "text": "1259"}, "Update the dependencies array to be: [cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", {"range": "1260", "text": "1261"}, "Update the dependencies array to be: [certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", {"range": "1262", "text": "1263"}, "Update the dependencies array to be: [calculateStatistics, cavi, certificazioni]", {"range": "1264", "text": "1265"}, "Update the dependencies array to be: [activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", {"range": "1266", "text": "1267"}, "Update the dependencies array to be: [cantiereId, loadComande, loadStatistiche]", {"range": "1268", "text": "1269"}, "Update the dependencies array to be: [getInitialAction, location.pathname]", {"range": "1270", "text": "1271"}, "Update the dependencies array to be: [certificazioneId, loadProve]", {"range": "1272", "text": "1273"}, "Update the dependencies array to be: [loadCaviDisponibili, open, tipoComanda]", {"range": "1274", "text": "1275"}, "Update the dependencies array to be: [searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", {"range": "1276", "text": "1277"}, [24744, 24753], "[caviAttivi, caviSpare, error, filters, user]", [1559, 1571], "[cantiereId, selectCantiere]", [3538, 3550], "[cantiereId, loadCertificazioni]", [6583, 6585], "[handleOptionSelect, initialOption, loadBobine]", [3043, 3058], "[initialOption, loadCavi]", [1578, 1606], "[certificazione, cantiereId, loadCavi]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1077, 1089], "[cantiereId, loadCavi]", [3142, 3154], [3288, 3302], "[load<PERSON><PERSON><PERSON>, selected<PERSON>avo]", [3868, 3896], "[cavoId, cavi, selected<PERSON>av<PERSON>, onError]", [4325, 4337], "[cantiereId, loadBobine]", [1912, 1938], "[open, bobina, cantiereId, loadCavi]", [3608, 3620], "[cantiereId, loadInitialData]", [3705, 3751], "[cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", [3835, 3891], "[certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", [3997, 4019], "[calculateStatistics, cavi, certificazioni]", [4241, 4274], "[activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", [1648, 1660], "[cantiereId, loadComande, loadStatistiche]", [4982, 5001], "[getInitialAction, location.pathname]", [2516, 2534], "[certificazioneId, loadProve]", [1523, 1542], "[loadCaviDisponibili, open, tipoComanda]", [11274, 11326], "[searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]"]