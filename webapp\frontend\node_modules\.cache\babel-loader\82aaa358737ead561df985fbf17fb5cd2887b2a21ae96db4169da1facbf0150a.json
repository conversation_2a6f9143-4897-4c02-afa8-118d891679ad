{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CaviFilterableTable.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, Checkbox, Button } from '@mui/material';\nimport { CheckBox as CheckBoxIcon, Clear as ClearIcon, Straighten as RulerIcon, Settings as SettingsIcon, PlayArrow as StartIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport SmartCaviFilter from './SmartCaviFilter';\nimport ContextMenu from '../common/ContextMenu';\nimport useContextMenu from '../../hooks/useContextMenu';\nimport { formatDate } from '../../utils/dateUtils';\n\n/**\n * Componente per visualizzare la lista dei cavi con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista dei cavi da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche\n * @param {boolean} props.selectionEnabled - Abilita la selezione dei cavi\n * @param {Array} props.selectedCavi - Array degli ID dei cavi selezionati\n * @param {Function} props.onSelectionChange - Funzione chiamata quando cambia la selezione\n * @param {Array} props.contextMenuItems - Array di elementi per il menu contestuale\n * @param {Function} props.onContextMenuAction - Funzione chiamata quando si clicca su un elemento del menu contestuale\n * @param {Function} props.onStatusAction - Funzione chiamata quando si clicca sul pulsante stato\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CaviFilterableTable = ({\n  cavi = [],\n  loading = false,\n  onFilteredDataChange = null,\n  revisioneCorrente = null,\n  selectionEnabled = false,\n  selectedCavi = [],\n  onSelectionChange = null,\n  onSelectionToggle = null,\n  contextMenuItems = [],\n  onContextMenuAction = null,\n  onStatusAction = null\n}) => {\n  _s();\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi);\n\n  // Hook per il menu contestuale\n  const {\n    contextMenu,\n    handleContextMenu,\n    closeContextMenu\n  } = useContextMenu();\n\n  // Aggiorna i dati filtrati quando cambiano i cavi\n  useEffect(() => {\n    setFilteredCavi(cavi);\n    setSmartFilteredCavi(cavi);\n  }, [cavi]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = data => {\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce il cambio dei dati dal filtro intelligente\n  const handleSmartFilterChange = data => {\n    console.log('CaviFilterableTable - Smart filter change:', {\n      originalCount: cavi.length,\n      filteredCount: data.length,\n      filteredIds: data.map(c => c.id_cavo)\n    });\n    setSmartFilteredCavi(data);\n    // Il filtro intelligente ha la priorità sui filtri Excel-like\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce la selezione di un singolo cavo\n  const handleCavoToggle = cavoId => {\n    if (!selectionEnabled || !onSelectionChange) return;\n    const isSelected = selectedCavi.includes(cavoId);\n    let newSelection;\n    if (isSelected) {\n      // Rimuovi dalla selezione\n      newSelection = selectedCavi.filter(id => id !== cavoId);\n      console.log(`Cavo ${cavoId} deselezionato`);\n    } else {\n      // Aggiungi alla selezione\n      newSelection = [...selectedCavi, cavoId];\n      console.log(`Cavo ${cavoId} selezionato`);\n    }\n    onSelectionChange(newSelection);\n\n    // Feedback visivo rapido (opzionale - può essere rimosso se troppo invasivo)\n    // Potresti aggiungere qui un piccolo toast o animazione\n  };\n\n  // Seleziona tutti i cavi visibili (filtrati)\n  const handleSelectAll = () => {\n    if (!selectionEnabled || !onSelectionChange) return;\n    const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);\n    const allSelected = visibleCaviIds.every(id => selectedCavi.includes(id));\n    if (allSelected) {\n      // Deseleziona tutti i cavi visibili\n      const newSelection = selectedCavi.filter(id => !visibleCaviIds.includes(id));\n      onSelectionChange(newSelection);\n    } else {\n      // Seleziona tutti i cavi visibili\n      const newSelection = [...new Set([...selectedCavi, ...visibleCaviIds])];\n      onSelectionChange(newSelection);\n    }\n  };\n\n  // Deseleziona tutti i cavi\n  const handleClearSelection = () => {\n    if (!selectionEnabled || !onSelectionChange) return;\n    onSelectionChange([]);\n  };\n\n  // Definizione delle colonne\n  const columns = [\n  // Colonna di selezione (solo se abilitata)\n  ...(selectionEnabled ? [{\n    field: 'selection',\n    headerName: '',\n    disableFilter: true,\n    disableSort: true,\n    width: 50,\n    align: 'center',\n    headerStyle: {\n      width: '50px',\n      padding: '4px'\n    },\n    cellStyle: {\n      width: '50px',\n      padding: '4px',\n      textAlign: 'center'\n    },\n    renderHeader: () => {\n      const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);\n      const allSelected = visibleCaviIds.length > 0 && visibleCaviIds.every(id => selectedCavi.includes(id));\n      const someSelected = visibleCaviIds.some(id => selectedCavi.includes(id));\n      return /*#__PURE__*/_jsxDEV(Checkbox, {\n        checked: allSelected,\n        indeterminate: someSelected && !allSelected,\n        onChange: handleSelectAll,\n        size: \"small\",\n        title: allSelected ? \"Deseleziona tutti\" : \"Seleziona tutti\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this);\n    },\n    renderCell: row => /*#__PURE__*/_jsxDEV(Checkbox, {\n      checked: selectedCavi.includes(row.id_cavo),\n      onChange: () => handleCavoToggle(row.id_cavo),\n      size: \"small\",\n      onClick: e => e.stopPropagation()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 9\n    }, this)\n  }] : []), {\n    field: 'id_cavo',\n    headerName: 'ID Cavo',\n    dataType: 'text',\n    headerStyle: {\n      fontWeight: 'bold'\n    }\n  },\n  // Colonna Revisione rimossa e spostata nella tabella delle statistiche\n  {\n    field: 'sistema',\n    headerName: 'Sistema',\n    dataType: 'text'\n  }, {\n    field: 'utility',\n    headerName: 'Utility',\n    dataType: 'text'\n  }, {\n    field: 'tipologia',\n    headerName: 'Tipologia',\n    dataType: 'text'\n  },\n  // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n  {\n    field: 'sezione',\n    headerName: 'Formazione',\n    dataType: 'text',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    }\n  }, {\n    field: 'metri_teorici',\n    headerName: 'Metri Teorici',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n  }, {\n    field: 'metratura_reale',\n    headerName: 'Metri Reali',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'\n  }, {\n    field: 'stato_installazione',\n    headerName: 'Stato',\n    dataType: 'text',\n    renderCell: row => {\n      // Usa sempre colore grigio per tutti gli stati\n      const color = 'default';\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.stato_installazione || 'N/D',\n        size: \"small\",\n        color: color,\n        variant: \"outlined\",\n        onClick: onStatusAction ? e => {\n          e.stopPropagation();\n          if (row.stato_installazione === 'Installato') {\n            onStatusAction(row, 'modify_reel', 'Modifica Bobina');\n          } else {\n            onStatusAction(row, 'insert_meters', 'Inserisci Metri Posati');\n          }\n        } : undefined,\n        sx: {\n          cursor: onStatusAction ? 'pointer' : 'default',\n          backgroundColor: onStatusAction ? '#2196f3' : undefined,\n          color: onStatusAction ? '#ffffff' : undefined,\n          '&:hover': onStatusAction ? {\n            backgroundColor: '#1976d2 !important',\n            color: '#ffffff !important'\n          } : {},\n          transform: 'none !important',\n          transition: 'background-color 0.2s ease',\n          '&:active': {\n            transform: 'none !important'\n          },\n          '&:focus': {\n            transform: 'none !important'\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    field: 'id_bobina',\n    headerName: 'Bobina',\n    dataType: 'text',\n    renderCell: row => {\n      // Gestione differenziata per null e BOBINA_VUOTA\n      if (row.id_bobina === null) {\n        // Per cavi non posati (id_bobina è null)\n        return '-';\n      } else if (row.id_bobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        return 'BOBINA VUOTA';\n      } else if (!row.id_bobina) {\n        // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)\n        return '-';\n      }\n\n      // Estrai solo il numero della bobina (parte dopo '_B')\n      const match = row.id_bobina.match(/_B(.+)$/);\n      return match ? match[1] : row.id_bobina;\n    }\n  }, {\n    field: 'timestamp',\n    headerName: 'Data Modifica',\n    dataType: 'date',\n    renderCell: row => formatDate(row.timestamp)\n  }, {\n    field: 'collegamenti',\n    headerName: 'Collegamenti',\n    dataType: 'string',\n    align: 'center',\n    cellStyle: {\n      textAlign: 'center'\n    },\n    renderCell: row => {\n      // Determina lo stato testuale intelligente basato sul valore numerico\n      const collegamenti = row.collegamenti || 0;\n      let statoTestuale = '';\n      let actionType = '';\n      let chipColor = 'default';\n      let icon = '';\n      if (collegamenti === 0) {\n        statoTestuale = '⚪⚪ Collega cavo';\n        actionType = 'connect_cable';\n        chipColor = 'default';\n      } else if (collegamenti === 1) {\n        statoTestuale = '🟢⚪ Completa collegamento';\n        actionType = 'connect_arrival';\n        chipColor = 'warning';\n      } else if (collegamenti === 2) {\n        statoTestuale = '⚪🟢 Completa collegamento';\n        actionType = 'connect_departure';\n        chipColor = 'warning';\n      } else if (collegamenti === 3) {\n        statoTestuale = '🟢🟢 Scollega cavo';\n        actionType = 'disconnect_cable';\n        chipColor = 'success';\n      } else {\n        statoTestuale = `Gestisci collegamenti`;\n        actionType = 'manage_connections';\n        chipColor = 'default';\n      }\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: statoTestuale,\n        size: \"small\",\n        color: chipColor,\n        variant: \"outlined\",\n        onClick: onStatusAction ? e => {\n          e.stopPropagation();\n          onStatusAction(row, actionType, statoTestuale);\n        } : undefined,\n        sx: {\n          cursor: onStatusAction ? 'pointer' : 'default',\n          backgroundColor: onStatusAction ? '#2196f3' : undefined,\n          color: onStatusAction ? '#ffffff' : undefined,\n          '&:hover': onStatusAction ? {\n            backgroundColor: '#1976d2 !important',\n            color: '#ffffff !important'\n          } : {},\n          transform: 'none !important',\n          transition: 'background-color 0.2s ease',\n          '&:active': {\n            transform: 'none !important'\n          },\n          '&:focus': {\n            transform: 'none !important'\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Se la selezione è abilitata, evidenzia le righe selezionate\n    const isSelected = selectionEnabled && selectedCavi.includes(row.id_cavo);\n    const selectedColor = 'rgba(25, 118, 210, 0.2)';\n    const restColor = '#f5f7fa'; // Grigio-azzurro chiarissimo\n    const hoverColor = 'rgba(33, 150, 243, 0.1)'; // Blu tecnico Material Blue 500 con opacità 10%\n\n    return /*#__PURE__*/_jsxDEV(TableRow, {\n      selected: isSelected,\n      hover: true,\n      onClick: selectionEnabled ? () => handleCavoToggle(row.id_cavo) : undefined,\n      onContextMenu: e => contextMenuItems.length > 0 ? handleContextMenu(e, row) : undefined,\n      sx: {\n        backgroundColor: `${isSelected ? selectedColor : restColor} !important`,\n        cursor: selectionEnabled ? 'pointer' : 'default',\n        '&:hover': {\n          backgroundColor: `${selectionEnabled ? selectedColor : hoverColor} !important`\n        }\n      },\n      children: columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n        align: column.align || 'left',\n        sx: {\n          ...column.cellStyle,\n          backgroundColor: 'inherit !important' // Eredita il colore dalla riga\n        },\n        children: column.renderCell ? column.renderCell(row) : row[column.field]\n      }, column.field, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 11\n      }, this))\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(SmartCaviFilter, {\n      cavi: cavi,\n      onFilteredDataChange: handleSmartFilterChange,\n      loading: loading,\n      selectionEnabled: selectionEnabled,\n      onSelectionToggle: onSelectionToggle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n      data: smartFilteredCavi,\n      columns: columns,\n      onFilteredDataChange: handleFilteredDataChange,\n      loading: loading,\n      emptyMessage: \"Nessun cavo disponibile\",\n      renderRow: renderRow\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContextMenu, {\n      open: contextMenu.open,\n      anchorPosition: contextMenu.anchorPosition,\n      onClose: closeContextMenu,\n      menuItems: typeof contextMenuItems === 'function' ? contextMenuItems(contextMenu.contextData) : contextMenuItems,\n      contextData: contextMenu.contextData\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 392,\n    columnNumber: 5\n  }, this);\n};\n_s(CaviFilterableTable, \"8HPaO1mqoR5Gc08zbVuoa6vh04g=\", false, function () {\n  return [useContextMenu];\n});\n_c = CaviFilterableTable;\nexport default CaviFilterableTable;\nvar _c;\n$RefreshReg$(_c, \"CaviFilterableTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Chip", "TableRow", "TableCell", "Checkbox", "<PERSON><PERSON>", "CheckBox", "CheckBoxIcon", "Clear", "ClearIcon", "<PERSON>en", "RulerIcon", "Settings", "SettingsIcon", "PlayArrow", "StartIcon", "FilterableTable", "SmartCaviFilter", "ContextMenu", "useContextMenu", "formatDate", "jsxDEV", "_jsxDEV", "CaviFilterableTable", "cavi", "loading", "onFilteredDataChange", "revisioneCorrente", "selectionEnabled", "<PERSON><PERSON><PERSON>", "onSelectionChange", "onSelectionToggle", "contextMenuItems", "onContextMenuAction", "onStatusAction", "_s", "filteredCavi", "setFilteredCavi", "smartFilteredCavi", "setSmartFilteredCavi", "contextMenu", "handleContextMenu", "closeContextMenu", "handleFilteredDataChange", "data", "handleSmartFilterChange", "console", "log", "originalCount", "length", "filteredCount", "filteredIds", "map", "c", "id_cavo", "handleCavoToggle", "cavoId", "isSelected", "includes", "newSelection", "filter", "id", "handleSelectAll", "visibleCaviIds", "cavo", "allSelected", "every", "Set", "handleClearSelection", "columns", "field", "headerName", "disableFilter", "disableSort", "width", "align", "headerStyle", "padding", "cellStyle", "textAlign", "renderHeader", "someSelected", "some", "checked", "indeterminate", "onChange", "size", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderCell", "row", "onClick", "e", "stopPropagation", "dataType", "fontWeight", "metri_te<PERSON>ci", "toFixed", "metratura_reale", "color", "label", "stato_installazione", "variant", "undefined", "sx", "cursor", "backgroundColor", "transform", "transition", "id_bobina", "match", "timestamp", "colle<PERSON>nti", "statoTestuale", "actionType", "chipColor", "icon", "renderRow", "index", "selectedColor", "restColor", "hoverColor", "selected", "hover", "onContextMenu", "children", "column", "emptyMessage", "open", "anchorPosition", "onClose", "menuItems", "contextData", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CaviFilterableTable.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, Checkbox, Button } from '@mui/material';\nimport {\n  CheckBox as CheckBoxIcon,\n  Clear as ClearIcon,\n  Straighten as RulerIcon,\n  Settings as SettingsIcon,\n  PlayArrow as StartIcon\n} from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport SmartCaviFilter from './SmartCaviFilter';\nimport ContextMenu from '../common/ContextMenu';\nimport useContextMenu from '../../hooks/useContextMenu';\nimport { formatDate } from '../../utils/dateUtils';\n\n/**\n * Componente per visualizzare la lista dei cavi con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista dei cavi da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche\n * @param {boolean} props.selectionEnabled - Abilita la selezione dei cavi\n * @param {Array} props.selectedCavi - Array degli ID dei cavi selezionati\n * @param {Function} props.onSelectionChange - Funzione chiamata quando cambia la selezione\n * @param {Array} props.contextMenuItems - Array di elementi per il menu contestuale\n * @param {Function} props.onContextMenuAction - Funzione chiamata quando si clicca su un elemento del menu contestuale\n * @param {Function} props.onStatusAction - Funzione chiamata quando si clicca sul pulsante stato\n */\nconst CaviFilterableTable = ({\n  cavi = [],\n  loading = false,\n  onFilteredDataChange = null,\n  revisioneCorrente = null,\n  selectionEnabled = false,\n  selectedCavi = [],\n  onSelectionChange = null,\n  onSelectionToggle = null,\n  contextMenuItems = [],\n  onContextMenuAction = null,\n  onStatusAction = null\n}) => {\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi);\n\n  // Hook per il menu contestuale\n  const { contextMenu, handleContextMenu, closeContextMenu } = useContextMenu();\n\n  // Aggiorna i dati filtrati quando cambiano i cavi\n  useEffect(() => {\n    setFilteredCavi(cavi);\n    setSmartFilteredCavi(cavi);\n  }, [cavi]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = (data) => {\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce il cambio dei dati dal filtro intelligente\n  const handleSmartFilterChange = (data) => {\n    console.log('CaviFilterableTable - Smart filter change:', {\n      originalCount: cavi.length,\n      filteredCount: data.length,\n      filteredIds: data.map(c => c.id_cavo)\n    });\n    setSmartFilteredCavi(data);\n    // Il filtro intelligente ha la priorità sui filtri Excel-like\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce la selezione di un singolo cavo\n  const handleCavoToggle = (cavoId) => {\n    if (!selectionEnabled || !onSelectionChange) return;\n\n    const isSelected = selectedCavi.includes(cavoId);\n    let newSelection;\n\n    if (isSelected) {\n      // Rimuovi dalla selezione\n      newSelection = selectedCavi.filter(id => id !== cavoId);\n      console.log(`Cavo ${cavoId} deselezionato`);\n    } else {\n      // Aggiungi alla selezione\n      newSelection = [...selectedCavi, cavoId];\n      console.log(`Cavo ${cavoId} selezionato`);\n    }\n\n    onSelectionChange(newSelection);\n\n    // Feedback visivo rapido (opzionale - può essere rimosso se troppo invasivo)\n    // Potresti aggiungere qui un piccolo toast o animazione\n  };\n\n  // Seleziona tutti i cavi visibili (filtrati)\n  const handleSelectAll = () => {\n    if (!selectionEnabled || !onSelectionChange) return;\n\n    const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);\n    const allSelected = visibleCaviIds.every(id => selectedCavi.includes(id));\n\n    if (allSelected) {\n      // Deseleziona tutti i cavi visibili\n      const newSelection = selectedCavi.filter(id => !visibleCaviIds.includes(id));\n      onSelectionChange(newSelection);\n    } else {\n      // Seleziona tutti i cavi visibili\n      const newSelection = [...new Set([...selectedCavi, ...visibleCaviIds])];\n      onSelectionChange(newSelection);\n    }\n  };\n\n  // Deseleziona tutti i cavi\n  const handleClearSelection = () => {\n    if (!selectionEnabled || !onSelectionChange) return;\n    onSelectionChange([]);\n  };\n\n\n\n  // Definizione delle colonne\n  const columns = [\n    // Colonna di selezione (solo se abilitata)\n    ...(selectionEnabled ? [{\n      field: 'selection',\n      headerName: '',\n      disableFilter: true,\n      disableSort: true,\n      width: 50,\n      align: 'center',\n      headerStyle: { width: '50px', padding: '4px' },\n      cellStyle: { width: '50px', padding: '4px', textAlign: 'center' },\n      renderHeader: () => {\n        const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);\n        const allSelected = visibleCaviIds.length > 0 && visibleCaviIds.every(id => selectedCavi.includes(id));\n        const someSelected = visibleCaviIds.some(id => selectedCavi.includes(id));\n\n        return (\n          <Checkbox\n            checked={allSelected}\n            indeterminate={someSelected && !allSelected}\n            onChange={handleSelectAll}\n            size=\"small\"\n            title={allSelected ? \"Deseleziona tutti\" : \"Seleziona tutti\"}\n          />\n        );\n      },\n      renderCell: (row) => (\n        <Checkbox\n          checked={selectedCavi.includes(row.id_cavo)}\n          onChange={() => handleCavoToggle(row.id_cavo)}\n          size=\"small\"\n          onClick={(e) => e.stopPropagation()}\n        />\n      )\n    }] : []),\n    {\n      field: 'id_cavo',\n      headerName: 'ID Cavo',\n      dataType: 'text',\n      headerStyle: { fontWeight: 'bold' }\n    },\n    // Colonna Revisione rimossa e spostata nella tabella delle statistiche\n    {\n      field: 'sistema',\n      headerName: 'Sistema',\n      dataType: 'text'\n    },\n    {\n      field: 'utility',\n      headerName: 'Utility',\n      dataType: 'text'\n    },\n    {\n      field: 'tipologia',\n      headerName: 'Tipologia',\n      dataType: 'text'\n    },\n    // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n    {\n      field: 'sezione',\n      headerName: 'Formazione',\n      dataType: 'text',\n      align: 'right',\n      cellStyle: { textAlign: 'right' }\n    },\n    {\n      field: 'metri_teorici',\n      headerName: 'Metri Teorici',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n    },\n    {\n      field: 'metratura_reale',\n      headerName: 'Metri Reali',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'\n    },\n    {\n      field: 'stato_installazione',\n      headerName: 'Stato',\n      dataType: 'text',\n      renderCell: (row) => {\n        // Usa sempre colore grigio per tutti gli stati\n        const color = 'default';\n\n        return (\n          <Chip\n            label={row.stato_installazione || 'N/D'}\n            size=\"small\"\n            color={color}\n            variant=\"outlined\"\n            onClick={onStatusAction ? (e) => {\n              e.stopPropagation();\n              if (row.stato_installazione === 'Installato') {\n                onStatusAction(row, 'modify_reel', 'Modifica Bobina');\n              } else {\n                onStatusAction(row, 'insert_meters', 'Inserisci Metri Posati');\n              }\n            } : undefined}\n            sx={{\n              cursor: onStatusAction ? 'pointer' : 'default',\n              backgroundColor: onStatusAction ? '#2196f3' : undefined,\n              color: onStatusAction ? '#ffffff' : undefined,\n              '&:hover': onStatusAction ? {\n                backgroundColor: '#1976d2 !important',\n                color: '#ffffff !important'\n              } : {},\n              transform: 'none !important',\n              transition: 'background-color 0.2s ease',\n              '&:active': {\n                transform: 'none !important'\n              },\n              '&:focus': {\n                transform: 'none !important'\n              }\n            }}\n          />\n        );\n      }\n    },\n    {\n      field: 'id_bobina',\n      headerName: 'Bobina',\n      dataType: 'text',\n      renderCell: (row) => {\n        // Gestione differenziata per null e BOBINA_VUOTA\n        if (row.id_bobina === null) {\n          // Per cavi non posati (id_bobina è null)\n          return '-';\n        } else if (row.id_bobina === 'BOBINA_VUOTA') {\n          // Per cavi posati senza bobina specifica\n          return 'BOBINA VUOTA';\n        } else if (!row.id_bobina) {\n          // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)\n          return '-';\n        }\n\n        // Estrai solo il numero della bobina (parte dopo '_B')\n        const match = row.id_bobina.match(/_B(.+)$/);\n        return match ? match[1] : row.id_bobina;\n      }\n    },\n    {\n      field: 'timestamp',\n      headerName: 'Data Modifica',\n      dataType: 'date',\n      renderCell: (row) => formatDate(row.timestamp)\n    },\n    {\n      field: 'collegamenti',\n      headerName: 'Collegamenti',\n      dataType: 'string',\n      align: 'center',\n      cellStyle: { textAlign: 'center' },\n      renderCell: (row) => {\n        // Determina lo stato testuale intelligente basato sul valore numerico\n        const collegamenti = row.collegamenti || 0;\n        let statoTestuale = '';\n        let actionType = '';\n        let chipColor = 'default';\n        let icon = '';\n\n        if (collegamenti === 0) {\n          statoTestuale = '⚪⚪ Collega cavo';\n          actionType = 'connect_cable';\n          chipColor = 'default';\n        } else if (collegamenti === 1) {\n          statoTestuale = '🟢⚪ Completa collegamento';\n          actionType = 'connect_arrival';\n          chipColor = 'warning';\n        } else if (collegamenti === 2) {\n          statoTestuale = '⚪🟢 Completa collegamento';\n          actionType = 'connect_departure';\n          chipColor = 'warning';\n        } else if (collegamenti === 3) {\n          statoTestuale = '🟢🟢 Scollega cavo';\n          actionType = 'disconnect_cable';\n          chipColor = 'success';\n        } else {\n          statoTestuale = `Gestisci collegamenti`;\n          actionType = 'manage_connections';\n          chipColor = 'default';\n        }\n\n        return (\n          <Chip\n            label={statoTestuale}\n            size=\"small\"\n            color={chipColor}\n            variant=\"outlined\"\n            onClick={onStatusAction ? (e) => {\n              e.stopPropagation();\n              onStatusAction(row, actionType, statoTestuale);\n            } : undefined}\n            sx={{\n              cursor: onStatusAction ? 'pointer' : 'default',\n              backgroundColor: onStatusAction ? '#2196f3' : undefined,\n              color: onStatusAction ? '#ffffff' : undefined,\n              '&:hover': onStatusAction ? {\n                backgroundColor: '#1976d2 !important',\n                color: '#ffffff !important'\n              } : {},\n              transform: 'none !important',\n              transition: 'background-color 0.2s ease',\n              '&:active': {\n                transform: 'none !important'\n              },\n              '&:focus': {\n                transform: 'none !important'\n              }\n            }}\n          />\n        );\n      }\n    }\n  ];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Se la selezione è abilitata, evidenzia le righe selezionate\n    const isSelected = selectionEnabled && selectedCavi.includes(row.id_cavo);\n    const selectedColor = 'rgba(25, 118, 210, 0.2)';\n    const restColor = '#f5f7fa'; // Grigio-azzurro chiarissimo\n    const hoverColor = 'rgba(33, 150, 243, 0.1)'; // Blu tecnico Material Blue 500 con opacità 10%\n\n    return (\n      <TableRow\n        key={index}\n        selected={isSelected}\n        hover\n        onClick={selectionEnabled ? () => handleCavoToggle(row.id_cavo) : undefined}\n        onContextMenu={(e) => contextMenuItems.length > 0 ? handleContextMenu(e, row) : undefined}\n        sx={{\n          backgroundColor: `${isSelected ? selectedColor : restColor} !important`,\n          cursor: selectionEnabled ? 'pointer' : 'default',\n          '&:hover': {\n            backgroundColor: `${selectionEnabled ? selectedColor : hoverColor} !important`\n          }\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            align={column.align || 'left'}\n            sx={{\n              ...column.cellStyle,\n              backgroundColor: 'inherit !important' // Eredita il colore dalla riga\n            }}\n          >\n            {column.renderCell ? column.renderCell(row) : row[column.field]}\n          </TableCell>\n        ))}\n      </TableRow>\n    );\n  };\n\n\n\n  return (\n    <Box>\n      {/* Filtro intelligente */}\n      <SmartCaviFilter\n        cavi={cavi}\n        onFilteredDataChange={handleSmartFilterChange}\n        loading={loading}\n        selectionEnabled={selectionEnabled}\n        onSelectionToggle={onSelectionToggle}\n      />\n\n\n\n      {/* Tabella con filtri Excel-like sui dati già filtrati dal filtro intelligente */}\n      <FilterableTable\n        data={smartFilteredCavi}\n        columns={columns}\n        onFilteredDataChange={handleFilteredDataChange}\n        loading={loading}\n        emptyMessage=\"Nessun cavo disponibile\"\n        renderRow={renderRow}\n      />\n\n      {/* Menu contestuale */}\n      <ContextMenu\n        open={contextMenu.open}\n        anchorPosition={contextMenu.anchorPosition}\n        onClose={closeContextMenu}\n        menuItems={typeof contextMenuItems === 'function' ? contextMenuItems(contextMenu.contextData) : contextMenuItems}\n        contextData={contextMenu.contextData}\n      />\n    </Box>\n  );\n};\n\nexport default CaviFilterableTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,eAAe;AAC5F,SACEC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,SAAS,EACvBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,SAAS,QACjB,qBAAqB;AAC5B,OAAOC,eAAe,MAAM,2BAA2B;AACvD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,UAAU,QAAQ,uBAAuB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,SAAAC,MAAA,IAAAC,OAAA;AAeA,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,IAAI,GAAG,EAAE;EACTC,OAAO,GAAG,KAAK;EACfC,oBAAoB,GAAG,IAAI;EAC3BC,iBAAiB,GAAG,IAAI;EACxBC,gBAAgB,GAAG,KAAK;EACxBC,YAAY,GAAG,EAAE;EACjBC,iBAAiB,GAAG,IAAI;EACxBC,iBAAiB,GAAG,IAAI;EACxBC,gBAAgB,GAAG,EAAE;EACrBC,mBAAmB,GAAG,IAAI;EAC1BC,cAAc,GAAG;AACnB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC2B,IAAI,CAAC;EACtD,MAAM,CAACc,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1C,QAAQ,CAAC2B,IAAI,CAAC;;EAEhE;EACA,MAAM;IAAEgB,WAAW;IAAEC,iBAAiB;IAAEC;EAAiB,CAAC,GAAGvB,cAAc,CAAC,CAAC;;EAE7E;EACArB,SAAS,CAAC,MAAM;IACduC,eAAe,CAACb,IAAI,CAAC;IACrBe,oBAAoB,CAACf,IAAI,CAAC;EAC5B,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMmB,wBAAwB,GAAIC,IAAI,IAAK;IACzCP,eAAe,CAACO,IAAI,CAAC;IACrB,IAAIlB,oBAAoB,EAAE;MACxBA,oBAAoB,CAACkB,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAID,IAAI,IAAK;IACxCE,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;MACxDC,aAAa,EAAExB,IAAI,CAACyB,MAAM;MAC1BC,aAAa,EAAEN,IAAI,CAACK,MAAM;MAC1BE,WAAW,EAAEP,IAAI,CAACQ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO;IACtC,CAAC,CAAC;IACFf,oBAAoB,CAACK,IAAI,CAAC;IAC1B;IACAP,eAAe,CAACO,IAAI,CAAC;IACrB,IAAIlB,oBAAoB,EAAE;MACxBA,oBAAoB,CAACkB,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAIC,MAAM,IAAK;IACnC,IAAI,CAAC5B,gBAAgB,IAAI,CAACE,iBAAiB,EAAE;IAE7C,MAAM2B,UAAU,GAAG5B,YAAY,CAAC6B,QAAQ,CAACF,MAAM,CAAC;IAChD,IAAIG,YAAY;IAEhB,IAAIF,UAAU,EAAE;MACd;MACAE,YAAY,GAAG9B,YAAY,CAAC+B,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKL,MAAM,CAAC;MACvDV,OAAO,CAACC,GAAG,CAAC,QAAQS,MAAM,gBAAgB,CAAC;IAC7C,CAAC,MAAM;MACL;MACAG,YAAY,GAAG,CAAC,GAAG9B,YAAY,EAAE2B,MAAM,CAAC;MACxCV,OAAO,CAACC,GAAG,CAAC,QAAQS,MAAM,cAAc,CAAC;IAC3C;IAEA1B,iBAAiB,CAAC6B,YAAY,CAAC;;IAE/B;IACA;EACF,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAClC,gBAAgB,IAAI,CAACE,iBAAiB,EAAE;IAE7C,MAAMiC,cAAc,GAAG3B,YAAY,CAACgB,GAAG,CAACY,IAAI,IAAIA,IAAI,CAACV,OAAO,CAAC;IAC7D,MAAMW,WAAW,GAAGF,cAAc,CAACG,KAAK,CAACL,EAAE,IAAIhC,YAAY,CAAC6B,QAAQ,CAACG,EAAE,CAAC,CAAC;IAEzE,IAAII,WAAW,EAAE;MACf;MACA,MAAMN,YAAY,GAAG9B,YAAY,CAAC+B,MAAM,CAACC,EAAE,IAAI,CAACE,cAAc,CAACL,QAAQ,CAACG,EAAE,CAAC,CAAC;MAC5E/B,iBAAiB,CAAC6B,YAAY,CAAC;IACjC,CAAC,MAAM;MACL;MACA,MAAMA,YAAY,GAAG,CAAC,GAAG,IAAIQ,GAAG,CAAC,CAAC,GAAGtC,YAAY,EAAE,GAAGkC,cAAc,CAAC,CAAC,CAAC;MACvEjC,iBAAiB,CAAC6B,YAAY,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMS,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACxC,gBAAgB,IAAI,CAACE,iBAAiB,EAAE;IAC7CA,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;;EAID;EACA,MAAMuC,OAAO,GAAG;EACd;EACA,IAAIzC,gBAAgB,GAAG,CAAC;IACtB0C,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE;MAAEF,KAAK,EAAE,MAAM;MAAEG,OAAO,EAAE;IAAM,CAAC;IAC9CC,SAAS,EAAE;MAAEJ,KAAK,EAAE,MAAM;MAAEG,OAAO,EAAE,KAAK;MAAEE,SAAS,EAAE;IAAS,CAAC;IACjEC,YAAY,EAAEA,CAAA,KAAM;MAClB,MAAMjB,cAAc,GAAG3B,YAAY,CAACgB,GAAG,CAACY,IAAI,IAAIA,IAAI,CAACV,OAAO,CAAC;MAC7D,MAAMW,WAAW,GAAGF,cAAc,CAACd,MAAM,GAAG,CAAC,IAAIc,cAAc,CAACG,KAAK,CAACL,EAAE,IAAIhC,YAAY,CAAC6B,QAAQ,CAACG,EAAE,CAAC,CAAC;MACtG,MAAMoB,YAAY,GAAGlB,cAAc,CAACmB,IAAI,CAACrB,EAAE,IAAIhC,YAAY,CAAC6B,QAAQ,CAACG,EAAE,CAAC,CAAC;MAEzE,oBACEvC,OAAA,CAAClB,QAAQ;QACP+E,OAAO,EAAElB,WAAY;QACrBmB,aAAa,EAAEH,YAAY,IAAI,CAAChB,WAAY;QAC5CoB,QAAQ,EAAEvB,eAAgB;QAC1BwB,IAAI,EAAC,OAAO;QACZC,KAAK,EAAEtB,WAAW,GAAG,mBAAmB,GAAG;MAAkB;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAEN,CAAC;IACDC,UAAU,EAAGC,GAAG,iBACdvE,OAAA,CAAClB,QAAQ;MACP+E,OAAO,EAAEtD,YAAY,CAAC6B,QAAQ,CAACmC,GAAG,CAACvC,OAAO,CAAE;MAC5C+B,QAAQ,EAAEA,CAAA,KAAM9B,gBAAgB,CAACsC,GAAG,CAACvC,OAAO,CAAE;MAC9CgC,IAAI,EAAC,OAAO;MACZQ,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC;IAAE;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC;EAEL,CAAC,CAAC,GAAG,EAAE,CAAC,EACR;IACErB,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrB0B,QAAQ,EAAE,MAAM;IAChBrB,WAAW,EAAE;MAAEsB,UAAU,EAAE;IAAO;EACpC,CAAC;EACD;EACA;IACE5B,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrB0B,QAAQ,EAAE;EACZ,CAAC,EACD;IACE3B,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrB0B,QAAQ,EAAE;EACZ,CAAC,EACD;IACE3B,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvB0B,QAAQ,EAAE;EACZ,CAAC;EACD;EACA;IACE3B,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,YAAY;IACxB0B,QAAQ,EAAE,MAAM;IAChBtB,KAAK,EAAE,OAAO;IACdG,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ;EAClC,CAAC,EACD;IACET,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,eAAe;IAC3B0B,QAAQ,EAAE,QAAQ;IAClBtB,KAAK,EAAE,OAAO;IACdG,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCa,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACM,aAAa,GAAGN,GAAG,CAACM,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;EAC1E,CAAC,EACD;IACE9B,KAAK,EAAE,iBAAiB;IACxBC,UAAU,EAAE,aAAa;IACzB0B,QAAQ,EAAE,QAAQ;IAClBtB,KAAK,EAAE,OAAO;IACdG,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCa,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACQ,eAAe,GAAGR,GAAG,CAACQ,eAAe,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG;EAC9E,CAAC,EACD;IACE9B,KAAK,EAAE,qBAAqB;IAC5BC,UAAU,EAAE,OAAO;IACnB0B,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAGC,GAAG,IAAK;MACnB;MACA,MAAMS,KAAK,GAAG,SAAS;MAEvB,oBACEhF,OAAA,CAACrB,IAAI;QACHsG,KAAK,EAAEV,GAAG,CAACW,mBAAmB,IAAI,KAAM;QACxClB,IAAI,EAAC,OAAO;QACZgB,KAAK,EAAEA,KAAM;QACbG,OAAO,EAAC,UAAU;QAClBX,OAAO,EAAE5D,cAAc,GAAI6D,CAAC,IAAK;UAC/BA,CAAC,CAACC,eAAe,CAAC,CAAC;UACnB,IAAIH,GAAG,CAACW,mBAAmB,KAAK,YAAY,EAAE;YAC5CtE,cAAc,CAAC2D,GAAG,EAAE,aAAa,EAAE,iBAAiB,CAAC;UACvD,CAAC,MAAM;YACL3D,cAAc,CAAC2D,GAAG,EAAE,eAAe,EAAE,wBAAwB,CAAC;UAChE;QACF,CAAC,GAAGa,SAAU;QACdC,EAAE,EAAE;UACFC,MAAM,EAAE1E,cAAc,GAAG,SAAS,GAAG,SAAS;UAC9C2E,eAAe,EAAE3E,cAAc,GAAG,SAAS,GAAGwE,SAAS;UACvDJ,KAAK,EAAEpE,cAAc,GAAG,SAAS,GAAGwE,SAAS;UAC7C,SAAS,EAAExE,cAAc,GAAG;YAC1B2E,eAAe,EAAE,oBAAoB;YACrCP,KAAK,EAAE;UACT,CAAC,GAAG,CAAC,CAAC;UACNQ,SAAS,EAAE,iBAAiB;UAC5BC,UAAU,EAAE,4BAA4B;UACxC,UAAU,EAAE;YACVD,SAAS,EAAE;UACb,CAAC;UACD,SAAS,EAAE;YACTA,SAAS,EAAE;UACb;QACF;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEN;EACF,CAAC,EACD;IACErB,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,QAAQ;IACpB0B,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAGC,GAAG,IAAK;MACnB;MACA,IAAIA,GAAG,CAACmB,SAAS,KAAK,IAAI,EAAE;QAC1B;QACA,OAAO,GAAG;MACZ,CAAC,MAAM,IAAInB,GAAG,CAACmB,SAAS,KAAK,cAAc,EAAE;QAC3C;QACA,OAAO,cAAc;MACvB,CAAC,MAAM,IAAI,CAACnB,GAAG,CAACmB,SAAS,EAAE;QACzB;QACA,OAAO,GAAG;MACZ;;MAEA;MACA,MAAMC,KAAK,GAAGpB,GAAG,CAACmB,SAAS,CAACC,KAAK,CAAC,SAAS,CAAC;MAC5C,OAAOA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGpB,GAAG,CAACmB,SAAS;IACzC;EACF,CAAC,EACD;IACE1C,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,eAAe;IAC3B0B,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAGC,GAAG,IAAKzE,UAAU,CAACyE,GAAG,CAACqB,SAAS;EAC/C,CAAC,EACD;IACE5C,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,cAAc;IAC1B0B,QAAQ,EAAE,QAAQ;IAClBtB,KAAK,EAAE,QAAQ;IACfG,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAS,CAAC;IAClCa,UAAU,EAAGC,GAAG,IAAK;MACnB;MACA,MAAMsB,YAAY,GAAGtB,GAAG,CAACsB,YAAY,IAAI,CAAC;MAC1C,IAAIC,aAAa,GAAG,EAAE;MACtB,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAIC,SAAS,GAAG,SAAS;MACzB,IAAIC,IAAI,GAAG,EAAE;MAEb,IAAIJ,YAAY,KAAK,CAAC,EAAE;QACtBC,aAAa,GAAG,iBAAiB;QACjCC,UAAU,GAAG,eAAe;QAC5BC,SAAS,GAAG,SAAS;MACvB,CAAC,MAAM,IAAIH,YAAY,KAAK,CAAC,EAAE;QAC7BC,aAAa,GAAG,2BAA2B;QAC3CC,UAAU,GAAG,iBAAiB;QAC9BC,SAAS,GAAG,SAAS;MACvB,CAAC,MAAM,IAAIH,YAAY,KAAK,CAAC,EAAE;QAC7BC,aAAa,GAAG,2BAA2B;QAC3CC,UAAU,GAAG,mBAAmB;QAChCC,SAAS,GAAG,SAAS;MACvB,CAAC,MAAM,IAAIH,YAAY,KAAK,CAAC,EAAE;QAC7BC,aAAa,GAAG,oBAAoB;QACpCC,UAAU,GAAG,kBAAkB;QAC/BC,SAAS,GAAG,SAAS;MACvB,CAAC,MAAM;QACLF,aAAa,GAAG,uBAAuB;QACvCC,UAAU,GAAG,oBAAoB;QACjCC,SAAS,GAAG,SAAS;MACvB;MAEA,oBACEhG,OAAA,CAACrB,IAAI;QACHsG,KAAK,EAAEa,aAAc;QACrB9B,IAAI,EAAC,OAAO;QACZgB,KAAK,EAAEgB,SAAU;QACjBb,OAAO,EAAC,UAAU;QAClBX,OAAO,EAAE5D,cAAc,GAAI6D,CAAC,IAAK;UAC/BA,CAAC,CAACC,eAAe,CAAC,CAAC;UACnB9D,cAAc,CAAC2D,GAAG,EAAEwB,UAAU,EAAED,aAAa,CAAC;QAChD,CAAC,GAAGV,SAAU;QACdC,EAAE,EAAE;UACFC,MAAM,EAAE1E,cAAc,GAAG,SAAS,GAAG,SAAS;UAC9C2E,eAAe,EAAE3E,cAAc,GAAG,SAAS,GAAGwE,SAAS;UACvDJ,KAAK,EAAEpE,cAAc,GAAG,SAAS,GAAGwE,SAAS;UAC7C,SAAS,EAAExE,cAAc,GAAG;YAC1B2E,eAAe,EAAE,oBAAoB;YACrCP,KAAK,EAAE;UACT,CAAC,GAAG,CAAC,CAAC;UACNQ,SAAS,EAAE,iBAAiB;UAC5BC,UAAU,EAAE,4BAA4B;UACxC,UAAU,EAAE;YACVD,SAAS,EAAE;UACb,CAAC;UACD,SAAS,EAAE;YACTA,SAAS,EAAE;UACb;QACF;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEN;EACF,CAAC,CACF;;EAED;EACA,MAAM6B,SAAS,GAAGA,CAAC3B,GAAG,EAAE4B,KAAK,KAAK;IAChC;IACA,MAAMhE,UAAU,GAAG7B,gBAAgB,IAAIC,YAAY,CAAC6B,QAAQ,CAACmC,GAAG,CAACvC,OAAO,CAAC;IACzE,MAAMoE,aAAa,GAAG,yBAAyB;IAC/C,MAAMC,SAAS,GAAG,SAAS,CAAC,CAAC;IAC7B,MAAMC,UAAU,GAAG,yBAAyB,CAAC,CAAC;;IAE9C,oBACEtG,OAAA,CAACpB,QAAQ;MAEP2H,QAAQ,EAAEpE,UAAW;MACrBqE,KAAK;MACLhC,OAAO,EAAElE,gBAAgB,GAAG,MAAM2B,gBAAgB,CAACsC,GAAG,CAACvC,OAAO,CAAC,GAAGoD,SAAU;MAC5EqB,aAAa,EAAGhC,CAAC,IAAK/D,gBAAgB,CAACiB,MAAM,GAAG,CAAC,GAAGR,iBAAiB,CAACsD,CAAC,EAAEF,GAAG,CAAC,GAAGa,SAAU;MAC1FC,EAAE,EAAE;QACFE,eAAe,EAAE,GAAGpD,UAAU,GAAGiE,aAAa,GAAGC,SAAS,aAAa;QACvEf,MAAM,EAAEhF,gBAAgB,GAAG,SAAS,GAAG,SAAS;QAChD,SAAS,EAAE;UACTiF,eAAe,EAAE,GAAGjF,gBAAgB,GAAG8F,aAAa,GAAGE,UAAU;QACnE;MACF,CAAE;MAAAI,QAAA,EAED3D,OAAO,CAACjB,GAAG,CAAE6E,MAAM,iBAClB3G,OAAA,CAACnB,SAAS;QAERwE,KAAK,EAAEsD,MAAM,CAACtD,KAAK,IAAI,MAAO;QAC9BgC,EAAE,EAAE;UACF,GAAGsB,MAAM,CAACnD,SAAS;UACnB+B,eAAe,EAAE,oBAAoB,CAAC;QACxC,CAAE;QAAAmB,QAAA,EAEDC,MAAM,CAACrC,UAAU,GAAGqC,MAAM,CAACrC,UAAU,CAACC,GAAG,CAAC,GAAGA,GAAG,CAACoC,MAAM,CAAC3D,KAAK;MAAC,GAP1D2D,MAAM,CAAC3D,KAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQR,CACZ;IAAC,GAxBG8B,KAAK;MAAAjC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAyBF,CAAC;EAEf,CAAC;EAID,oBACErE,OAAA,CAACvB,GAAG;IAAAiI,QAAA,gBAEF1G,OAAA,CAACL,eAAe;MACdO,IAAI,EAAEA,IAAK;MACXE,oBAAoB,EAAEmB,uBAAwB;MAC9CpB,OAAO,EAAEA,OAAQ;MACjBG,gBAAgB,EAAEA,gBAAiB;MACnCG,iBAAiB,EAAEA;IAAkB;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAKFrE,OAAA,CAACN,eAAe;MACd4B,IAAI,EAAEN,iBAAkB;MACxB+B,OAAO,EAAEA,OAAQ;MACjB3C,oBAAoB,EAAEiB,wBAAyB;MAC/ClB,OAAO,EAAEA,OAAQ;MACjByG,YAAY,EAAC,yBAAyB;MACtCV,SAAS,EAAEA;IAAU;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eAGFrE,OAAA,CAACJ,WAAW;MACViH,IAAI,EAAE3F,WAAW,CAAC2F,IAAK;MACvBC,cAAc,EAAE5F,WAAW,CAAC4F,cAAe;MAC3CC,OAAO,EAAE3F,gBAAiB;MAC1B4F,SAAS,EAAE,OAAOtG,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAACQ,WAAW,CAAC+F,WAAW,CAAC,GAAGvG,gBAAiB;MACjHuG,WAAW,EAAE/F,WAAW,CAAC+F;IAAY;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxD,EAAA,CAzYIZ,mBAAmB;EAAA,QAiBsCJ,cAAc;AAAA;AAAAqH,EAAA,GAjBvEjH,mBAAmB;AA2YzB,eAAeA,mBAAmB;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}