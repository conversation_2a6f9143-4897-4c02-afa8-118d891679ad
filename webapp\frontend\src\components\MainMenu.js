import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useGlobalContext } from '../context/GlobalContext';
import {
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  Typography,
  Collapse,
  ListItemButton
} from '@mui/material';
import {
  Home as HomeIcon,
  AdminPanelSettings as AdminIcon,
  Construction as ConstructionIcon,
  Cable as CableIcon,
  Description as ReportIcon,
  ExpandLess,
  ExpandMore,
  ViewList as ViewListIcon,
  Engineering as EngineeringIcon,
  Inventory as InventoryIcon,
  TableChart as TableChartIcon,
  Assessment as AssessmentIcon,
  VerifiedUser as VerifiedUserIcon,
  Verified as VerifiedIcon,
  ShoppingCart as ShoppingCartIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const MainMenu = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, isImpersonating, impersonatedUser } = useAuth();
  const { setOpenEliminaCavoDialog, setOpenModificaCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();

  // Funzione di utilità per creare ListItemText con dimensione del testo ridotta
  const createListItemText = (primary, level = 1) => {
    // Dimensioni del testo in base al livello del menu - ridotte ulteriormente
    const fontSize = level === 1 ? '0.75rem' : level === 2 ? '0.7rem' : '0.65rem';
    return (
      <ListItemText
        primary={primary}
        primaryTypographyProps={{ fontSize }}
        sx={{ my: 0, py: 0 }} // Riduce il margine e padding verticale
      />
    );
  };

  // Stati per i menu a cascata - per utenti cantiere, il menu cavi è aperto di default
  const [openCaviMenu, setOpenCaviMenu] = useState(user?.role === 'cantieri_user');
  const [openCantieriMenu, setOpenCantieriMenu] = useState(false);
  const [openAdminMenu, setOpenAdminMenu] = useState(false);
  const [openPosaMenu, setOpenPosaMenu] = useState(false);
  const [openParcoMenu, setOpenParcoMenu] = useState(false);
  const [openExcelMenu, setOpenExcelMenu] = useState(false);
  const [openCertificazioneMenu, setOpenCertificazioneMenu] = useState(false);
  const [openCertificazioneCEIMenu, setOpenCertificazioneCEIMenu] = useState(false);

  // Recupera l'ID del cantiere selezionato dal localStorage
  const selectedCantiereId = localStorage.getItem('selectedCantiereId');
  const selectedCantiereName = localStorage.getItem('selectedCantiereName');

  // Verifica se un percorso è attivo
  const isActive = (path) => {
    return location.pathname === path;
  };

  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)
  const isPartOfActive = (path) => {
    return location.pathname.startsWith(path);
  };

  // Gestisce l'apertura/chiusura dei menu a cascata
  const handleToggleCaviMenu = () => {
    setOpenCaviMenu(!openCaviMenu);
  };

  const handleToggleCantieriMenu = () => {
    setOpenCantieriMenu(!openCantieriMenu);
  };

  const handleToggleAdminMenu = () => {
    setOpenAdminMenu(!openAdminMenu);
  };

  // Naviga a un percorso
  const navigateTo = (path) => {
    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);
    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,
    // reindirizza al menu amministratore invece che alla home generica
    if (path === '/dashboard' && isImpersonating) {
      console.log('Utente impersonato, reindirizzamento al menu amministratore');
      navigate('/dashboard/admin');
    } else {
      navigate(path);
    }
  };

  return (
    <List dense sx={{ '& .MuiListItemButton-root': { py: 0.2, minHeight: '28px' } }}>
      {/* Home - Se l'utente è un amministratore che sta impersonando un utente, mostra "Torna al Menu Admin" */}
      <ListItemButton
        selected={isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard')}
        onClick={() => navigateTo('/dashboard')}
      >
        <ListItemIcon>
          <HomeIcon fontSize="small" />
        </ListItemIcon>
        {createListItemText(isImpersonating ? "Torna al Menu Admin" : "Home", 1)}
      </ListItemButton>

      {/* Menu Amministratore (solo per admin) */}
      {user?.role === 'owner' && (
        <>
          <Divider />
          <ListItemButton
            onClick={handleToggleAdminMenu}
            selected={isPartOfActive('/dashboard/admin')}
          >
            <ListItemIcon>
              <AdminIcon fontSize="small" />
            </ListItemIcon>
            {createListItemText("Amministrazione", 1)}
            {openAdminMenu ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
          </ListItemButton>
          <Collapse in={openAdminMenu} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              <ListItemButton
                sx={{ pl: 2 }}
                selected={isActive('/dashboard/admin')}
                onClick={() => navigateTo('/dashboard/admin')}
              >

                {createListItemText("Pannello Admin", 2)}
              </ListItemButton>
              {/* Altri sottomenu admin possono essere aggiunti qui */}
            </List>
          </Collapse>
        </>
      )}

      {/* Menu per utenti standard e cantieri */}
      {/* Mostra per utenti standard/cantiere o per admin che sta impersonando un utente */}
      {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (
        <>
          <Divider />
          {isImpersonating && impersonatedUser && (
            <Box sx={{ p: 1, bgcolor: 'rgba(255, 165, 0, 0.1)', borderLeft: '4px solid orange' }}>
              <Typography variant="caption" color="textSecondary">
                Accesso come utente:
              </Typography>
              <Typography variant="caption" fontWeight="bold">
                {impersonatedUser.username}
              </Typography>
            </Box>
          )}

          {/* Menu Cantieri con sottomenu - visibile solo per utenti standard o admin che impersonano */}
          {(user?.role === 'user' || isImpersonating) && (
            <>
              <ListItemButton
                onClick={handleToggleCantieriMenu}
                selected={isPartOfActive('/dashboard/cantieri')}
              >
                <ListItemIcon>
                  <ConstructionIcon fontSize="small" />
                </ListItemIcon>
                {createListItemText(isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : "I Miei Cantieri", 1)}
                {openCantieriMenu ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
              </ListItemButton>
              <Collapse in={openCantieriMenu} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  <ListItemButton
                    sx={{ pl: 2 }}
                    selected={isActive('/dashboard/cantieri')}
                    onClick={() => navigateTo('/dashboard/cantieri')}
                  >
                    {createListItemText("Lista Cantieri", 2)}
                  </ListItemButton>

                  {/* Mostra il cantiere selezionato se presente */}
                  {selectedCantiereId && (
                    <>
                      <ListItemButton
                        sx={{ pl: 2 }}
                        selected={isActive(`/dashboard/cantieri/${selectedCantiereId}`)}
                        onClick={() => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`)}
                      >

                        {createListItemText(`Cantiere: ${selectedCantiereName || selectedCantiereId}`, 2)}
                      </ListItemButton>
                    </>
                  )}
                </List>
              </Collapse>
            </>
          )}

          {/* Menu Cavi con sottomenu - visibile solo se un cantiere è selezionato */}
          {selectedCantiereId && (
            <ListItemButton
              onClick={handleToggleCaviMenu}
              selected={isPartOfActive('/dashboard/cavi')}
            >
              <ListItemIcon>
                <CableIcon fontSize="small" />
              </ListItemIcon>
              {createListItemText(`Gestione Cavi (${selectedCantiereName || selectedCantiereId})`, 1)}
              {openCaviMenu ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
            </ListItemButton>
          )}

          {selectedCantiereId && (
            <Collapse in={openCaviMenu} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                {/* Visualizza Cavi - nascosto per utenti cantiere perché ridondante con il tasto Home */}
                {user?.role !== 'cantieri_user' && (
                  <ListItemButton
                    sx={{ pl: 2 }}
                    selected={isActive('/dashboard/cavi/visualizza')}
                    onClick={() => navigateTo('/dashboard/cavi/visualizza')}
                  >
                    {createListItemText("Visualizza Cavi", 2)}
                  </ListItemButton>
                )}

                {/* Posa e Collegamenti con sottomenu */}
                <ListItemButton
                  sx={{ pl: 2 }}
                  onClick={() => setOpenPosaMenu(!openPosaMenu)}
                  selected={isPartOfActive('/dashboard/cavi/posa')}
                >

                  {createListItemText("Posa e Collegamenti", 2)}
                  {openPosaMenu ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
                </ListItemButton>

                <Collapse in={openPosaMenu} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    <ListItemButton
                      sx={{ pl: 3 }}
                      selected={isActive('/dashboard/cavi/posa/inserisci-metri')}
                      onClick={() => navigateTo('/dashboard/cavi/posa/inserisci-metri')}
                    >

                      {createListItemText("Inserisci metri posati", 3)}
                    </ListItemButton>

                    <ListItemButton
                      sx={{ pl: 3 }}
                      selected={isActive('/dashboard/cavi/posa/modifica-cavo')}
                      onClick={() => {
                        // Prima naviga alla pagina visualizza cavi
                        navigateTo('/dashboard/cavi/visualizza');
                        // Poi apre il dialogo di modifica cavi con un piccolo ritardo
                        setTimeout(() => {
                          setOpenModificaCavoDialog(true);
                        }, 500);
                      }}
                    >
                      {createListItemText("Modifica cavo", 3)}
                    </ListItemButton>

                    <ListItemButton
                      sx={{ pl: 3 }}
                      selected={isActive('/dashboard/cavi/posa/aggiungi-cavo')}
                      onClick={() => {
                        // Prima naviga alla pagina visualizza cavi
                        navigateTo('/dashboard/cavi/visualizza');
                        // Poi apre il dialogo di aggiunta cavi con un piccolo ritardo
                        setTimeout(() => {
                          setOpenAggiungiCavoDialog(true);
                        }, 500);
                      }}
                    >
                      {createListItemText("Aggiungi nuovo cavo", 3)}
                    </ListItemButton>

                    <ListItemButton
                      sx={{ pl: 3 }}
                      selected={isActive('/dashboard/cavi/posa/elimina-cavo')}
                      onClick={() => {
                        // Apre il dialogo di eliminazione cavi invece di navigare alla pagina
                        setOpenEliminaCavoDialog(true);
                        // Naviga alla pagina visualizza cavi
                        navigateTo('/dashboard/cavi/visualizza');
                      }}
                    >
                      {createListItemText("Elimina cavo", 3)}
                    </ListItemButton>

                    <ListItemButton
                      sx={{ pl: 3 }}
                      selected={isActive('/dashboard/cavi/posa/modifica-bobina')}
                      onClick={() => navigateTo('/dashboard/cavi/posa/modifica-bobina')}
                    >
                      {createListItemText("Modifica bobina cavo posato", 3)}
                    </ListItemButton>
                  </List>
                </Collapse>

                {/* Parco Cavi con sottomenu */}
                <ListItemButton
                  sx={{ pl: 2 }}
                  onClick={() => setOpenParcoMenu(!openParcoMenu)}
                  selected={isPartOfActive('/dashboard/cavi/parco')}
                >

                  {createListItemText("Parco Cavi", 2)}
                  {openParcoMenu ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
                </ListItemButton>

                <Collapse in={openParcoMenu} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    <ListItemButton
                      sx={{ pl: 3 }}
                      selected={isActive('/dashboard/cavi/parco/visualizza')}
                      onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}
                    >
                      {createListItemText("Visualizza Bobine", 3)}
                    </ListItemButton>

                    <ListItemButton
                      sx={{ pl: 3 }}
                      selected={isActive('/dashboard/cavi/parco/crea')}
                      onClick={() => navigateTo('/dashboard/cavi/parco/crea')}
                    >
                      {createListItemText("Crea Nuova Bobina", 3)}
                    </ListItemButton>

                    <ListItemButton
                      sx={{ pl: 3 }}
                      selected={isActive('/dashboard/cavi/parco/modifica')}
                      onClick={() => navigateTo('/dashboard/cavi/parco/modifica')}
                    >
                      {createListItemText("Modifica Bobina", 3)}
                    </ListItemButton>

                    <ListItemButton
                      sx={{ pl: 3 }}
                      selected={isActive('/dashboard/cavi/parco/elimina')}
                      onClick={() => navigateTo('/dashboard/cavi/parco/elimina')}
                    >
                      {createListItemText("Elimina Bobina", 3)}
                    </ListItemButton>

                    <ListItemButton
                      sx={{ pl: 3 }}
                      selected={isActive('/dashboard/cavi/parco/storico')}
                      onClick={() => navigateTo('/dashboard/cavi/parco/storico')}
                    >
                      {createListItemText("Storico Utilizzo", 3)}
                    </ListItemButton>
                  </List>
                </Collapse>

                {/* Gestione Excel con sottomenu */}
                <ListItemButton
                  sx={{ pl: 2 }}
                  selected={isActive('/dashboard/cavi/excel')}
                  onClick={() => navigateTo('/dashboard/cavi/excel')}
                >
                  {createListItemText("Gestione Excel", 2)}
                </ListItemButton>

                {/* Report - collegamento diretto */}
                <ListItemButton
                  sx={{ pl: 2 }}
                  selected={isPartOfActive('/dashboard/cavi/report')}
                  onClick={() => navigateTo(`/dashboard/cavi/${selectedCantiereId}/report`)}
                >
                  {createListItemText("Report", 2)}
                </ListItemButton>

                {/* Certificazione Cavi con sottomenu CEI 64-8 */}
                <ListItemButton
                  sx={{ pl: 2 }}
                  onClick={() => setOpenCertificazioneMenu(!openCertificazioneMenu)}
                  selected={isPartOfActive('/dashboard/cavi/certificazione')}
                >
                  <ListItemIcon>
                    <VerifiedIcon fontSize="small" />
                  </ListItemIcon>
                  {createListItemText("Certificazione Cavi", 2)}
                  {openCertificazioneMenu ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
                </ListItemButton>

                <Collapse in={openCertificazioneMenu} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {/* Certificazione Tradizionale */}
                    <ListItemButton
                      sx={{ pl: 3 }}
                      selected={isActive('/dashboard/cavi/certificazione')}
                      onClick={() => navigateTo('/dashboard/cavi/certificazione')}
                    >
                      {createListItemText("Certificazione Tradizionale", 3)}
                    </ListItemButton>

                    {/* Certificazione CEI 64-8 con sottomenu */}
                    <ListItemButton
                      sx={{ pl: 3 }}
                      onClick={() => setOpenCertificazioneCEIMenu(!openCertificazioneCEIMenu)}
                      selected={isPartOfActive('/dashboard/cavi/certificazione-cei')}
                    >
                      {createListItemText("Certificazione CEI 64-8", 3)}
                      {openCertificazioneCEIMenu ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
                    </ListItemButton>

                    <Collapse in={openCertificazioneCEIMenu} timeout="auto" unmountOnExit>
                      <List component="div" disablePadding>
                        <ListItemButton
                          sx={{ pl: 4 }}
                          selected={isActive('/dashboard/cavi/certificazione-cei/dashboard')}
                          onClick={() => navigateTo('/dashboard/cavi/certificazione-cei/dashboard')}
                        >
                          {createListItemText("Dashboard CEI 64-8", 4)}
                        </ListItemButton>

                        <ListItemButton
                          sx={{ pl: 4 }}
                          selected={isActive('/dashboard/cavi/certificazione-cei/rapporti')}
                          onClick={() => navigateTo('/dashboard/cavi/certificazione-cei/rapporti')}
                        >
                          {createListItemText("Rapporti Generali", 4)}
                        </ListItemButton>

                        <ListItemButton
                          sx={{ pl: 4 }}
                          selected={isActive('/dashboard/cavi/certificazione-cei/prove')}
                          onClick={() => navigateTo('/dashboard/cavi/certificazione-cei/prove')}
                        >
                          {createListItemText("Prove Dettagliate", 4)}
                        </ListItemButton>

                        <ListItemButton
                          sx={{ pl: 4 }}
                          selected={isActive('/dashboard/cavi/certificazione-cei/non-conformita')}
                          onClick={() => navigateTo('/dashboard/cavi/certificazione-cei/non-conformita')}
                        >
                          {createListItemText("Non Conformità", 4)}
                        </ListItemButton>

                        <ListItemButton
                          sx={{ pl: 4 }}
                          selected={isActive('/dashboard/cavi/certificazione-cei/strumenti')}
                          onClick={() => navigateTo('/dashboard/cavi/certificazione-cei/strumenti')}
                        >
                          {createListItemText("Strumenti Certificati", 4)}
                        </ListItemButton>
                      </List>
                    </Collapse>
                  </List>
                </Collapse>

                {/* Gestione Comande - semplificato */}
                <ListItemButton
                  sx={{ pl: 2 }}
                  selected={isActive('/dashboard/cavi/comande')}
                  onClick={() => navigateTo('/dashboard/cavi/comande')}
                >
                  {createListItemText("Gestione Comande", 2)}
                </ListItemButton>

                {/* Accesso Rapido Comanda */}
                <ListItemButton
                  sx={{ pl: 2 }}
                  selected={isActive('/dashboard/accesso-rapido-comanda')}
                  onClick={() => navigateTo('/dashboard/accesso-rapido-comanda')}
                >
                  {createListItemText("Accesso Rapido Comanda", 2)}
                </ListItemButton>
              </List>
            </Collapse>
          )}
        </>
      )}
    </List>
  );
};

export default MainMenu;
