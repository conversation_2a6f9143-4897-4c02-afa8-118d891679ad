{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { useAuth } from './context/AuthContext';\nimport LoginPage from './pages/LoginPageNew';\nimport Dashboard from './pages/Dashboard';\nimport ProtectedRoute from './components/ProtectedRoute';\n\n// Tema personalizzato\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#0d47a1',\n      contrastText: '#ffffff'\n    },\n    secondary: {\n      main: '#dc004e',\n      light: '#ff5c8d',\n      dark: '#9a0036',\n      contrastText: '#ffffff'\n    },\n    info: {\n      main: '#0288d1',\n      light: '#03dac6',\n      dark: '#018786',\n      contrastText: '#ffffff'\n    },\n    success: {\n      main: '#2e7d32',\n      light: '#4caf50',\n      dark: '#1b5e20',\n      contrastText: '#ffffff'\n    },\n    warning: {\n      main: '#ed6c02',\n      light: '#ff9800',\n      dark: '#e65100',\n      contrastText: '#ffffff'\n    },\n    error: {\n      main: '#d32f2f',\n      light: '#f44336',\n      dark: '#c62828',\n      contrastText: '#ffffff'\n    }\n  }\n});\nfunction App() {\n  _s();\n  const {\n    isAuthenticated,\n    loading,\n    user\n  } = useAuth();\n  console.log('App - Stato autenticazione:', {\n    isAuthenticated,\n    loading\n  });\n\n  // Se l'applicazione è in caricamento, mostra un indicatore di caricamento\n  if (loading) {\n    console.log('App - Mostrando indicatore di caricamento');\n    return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '100vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Caricamento...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this);\n  }\n  console.log('App - Rendering principale, isAuthenticated:', isAuthenticated);\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: isAuthenticated ? (user === null || user === void 0 ? void 0 : user.role) === 'owner' ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard/admin\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 38\n        }, this) : (user === null || user === void 0 ? void 0 : user.role) === 'user' ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard/cantieri\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 37\n        }, this) : (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user' ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard/cavi/visualizza\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 46\n        }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard/*\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: isAuthenticated ? (user === null || user === void 0 ? void 0 : user.role) === 'owner' ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard/admin\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 40\n        }, this) : (user === null || user === void 0 ? void 0 : user.role) === 'user' ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard/cantieri\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 39\n        }, this) : (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user' ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard/cavi/visualizza\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 48\n        }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/login\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"8HNWTDX9ZYet8YmVN2bU91bK+h8=\", false, function () {\n  return [useAuth];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "ThemeProvider", "createTheme", "CssBaseline", "useAuth", "LoginPage", "Dashboard", "ProtectedRoute", "jsxDEV", "_jsxDEV", "theme", "palette", "primary", "main", "light", "dark", "contrastText", "secondary", "info", "success", "warning", "error", "App", "_s", "isAuthenticated", "loading", "user", "console", "log", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "justifyContent", "alignItems", "height", "textAlign", "path", "element", "role", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\n\nimport { useAuth } from './context/AuthContext';\nimport LoginPage from './pages/LoginPageNew';\nimport Dashboard from './pages/Dashboard';\nimport ProtectedRoute from './components/ProtectedRoute';\n\n// Tema personalizzato\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#0d47a1',\n      contrastText: '#ffffff',\n    },\n    secondary: {\n      main: '#dc004e',\n      light: '#ff5c8d',\n      dark: '#9a0036',\n      contrastText: '#ffffff',\n    },\n    info: {\n      main: '#0288d1',\n      light: '#03dac6',\n      dark: '#018786',\n      contrastText: '#ffffff',\n    },\n    success: {\n      main: '#2e7d32',\n      light: '#4caf50',\n      dark: '#1b5e20',\n      contrastText: '#ffffff',\n    },\n    warning: {\n      main: '#ed6c02',\n      light: '#ff9800',\n      dark: '#e65100',\n      contrastText: '#ffffff',\n    },\n    error: {\n      main: '#d32f2f',\n      light: '#f44336',\n      dark: '#c62828',\n      contrastText: '#ffffff',\n    },\n  },\n});\n\nfunction App() {\n  const { isAuthenticated, loading, user } = useAuth();\n\n  console.log('App - Stato autenticazione:', { isAuthenticated, loading });\n\n  // Se l'applicazione è in caricamento, mostra un indicatore di caricamento\n  if (loading) {\n    console.log('App - Mostrando indicatore di caricamento');\n    return (\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\n          <div style={{ textAlign: 'center' }}>\n            <div>Caricamento...</div>\n          </div>\n        </div>\n      </ThemeProvider>\n    );\n  }\n\n  console.log('App - Rendering principale, isAuthenticated:', isAuthenticated);\n\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Routes>\n          <Route path=\"/login\" element={\n          isAuthenticated ? (\n            user?.role === 'owner' ? <Navigate to=\"/dashboard/admin\" replace /> :\n            user?.role === 'user' ? <Navigate to=\"/dashboard/cantieri\" replace /> :\n            user?.role === 'cantieri_user' ? <Navigate to=\"/dashboard/cavi/visualizza\" replace /> :\n            <Navigate to=\"/dashboard\" replace />\n          ) : <LoginPage />\n        } />\n        <Route\n          path=\"/dashboard/*\"\n          element={\n            <ProtectedRoute>\n              <Dashboard />\n            </ProtectedRoute>\n          }\n        />\n        <Route\n          path=\"/\"\n          element={\n            isAuthenticated ? (\n              user?.role === 'owner' ? <Navigate to=\"/dashboard/admin\" replace /> :\n              user?.role === 'user' ? <Navigate to=\"/dashboard/cantieri\" replace /> :\n              user?.role === 'cantieri_user' ? <Navigate to=\"/dashboard/cavi/visualizza\" replace /> :\n              <Navigate to=\"/dashboard\" replace />\n            ) : (\n              <Navigate to=\"/login\" replace />\n            )\n          }\n        />\n      </Routes>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AAEnD,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,cAAc,MAAM,6BAA6B;;AAExD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAGR,WAAW,CAAC;EACxBS,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDC,SAAS,EAAE;MACTJ,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDE,IAAI,EAAE;MACJL,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDG,OAAO,EAAE;MACPN,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDI,OAAO,EAAE;MACPP,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDK,KAAK,EAAE;MACLR,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB;EACF;AACF,CAAC,CAAC;AAEF,SAASM,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC,eAAe;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGtB,OAAO,CAAC,CAAC;EAEpDuB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;IAAEJ,eAAe;IAAEC;EAAQ,CAAC,CAAC;;EAExE;EACA,IAAIA,OAAO,EAAE;IACXE,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IACxD,oBACEnB,OAAA,CAACR,aAAa;MAACS,KAAK,EAAEA,KAAM;MAAAmB,QAAA,gBAC1BpB,OAAA,CAACN,WAAW;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfxB,OAAA;QAAKyB,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAT,QAAA,eAC/FpB,OAAA;UAAKyB,KAAK,EAAE;YAAEK,SAAS,EAAE;UAAS,CAAE;UAAAV,QAAA,eAClCpB,OAAA;YAAAoB,QAAA,EAAK;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAEpB;EAEAN,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEJ,eAAe,CAAC;EAE5E,oBACEf,OAAA,CAACR,aAAa;IAACS,KAAK,EAAEA,KAAM;IAAAmB,QAAA,gBAC1BpB,OAAA,CAACN,WAAW;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfxB,OAAA,CAACX,MAAM;MAAA+B,QAAA,gBACHpB,OAAA,CAACV,KAAK;QAACyC,IAAI,EAAC,QAAQ;QAACC,OAAO,EAC5BjB,eAAe,GACb,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,MAAK,OAAO,gBAAGjC,OAAA,CAACT,QAAQ;UAAC2C,EAAE,EAAC,kBAAkB;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACnE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,MAAK,MAAM,gBAAGjC,OAAA,CAACT,QAAQ;UAAC2C,EAAE,EAAC,qBAAqB;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACrE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,MAAK,eAAe,gBAAGjC,OAAA,CAACT,QAAQ;UAAC2C,EAAE,EAAC,4BAA4B;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBACrFxB,OAAA,CAACT,QAAQ;UAAC2C,EAAE,EAAC,YAAY;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAClCxB,OAAA,CAACJ,SAAS;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACJxB,OAAA,CAACV,KAAK;QACJyC,IAAI,EAAC,cAAc;QACnBC,OAAO,eACLhC,OAAA,CAACF,cAAc;UAAAsB,QAAA,eACbpB,OAAA,CAACH,SAAS;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFxB,OAAA,CAACV,KAAK;QACJyC,IAAI,EAAC,GAAG;QACRC,OAAO,EACLjB,eAAe,GACb,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,MAAK,OAAO,gBAAGjC,OAAA,CAACT,QAAQ;UAAC2C,EAAE,EAAC,kBAAkB;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACnE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,MAAK,MAAM,gBAAGjC,OAAA,CAACT,QAAQ;UAAC2C,EAAE,EAAC,qBAAqB;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACrE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,MAAK,eAAe,gBAAGjC,OAAA,CAACT,QAAQ;UAAC2C,EAAE,EAAC,4BAA4B;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBACrFxB,OAAA,CAACT,QAAQ;UAAC2C,EAAE,EAAC,YAAY;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpCxB,OAAA,CAACT,QAAQ;UAAC2C,EAAE,EAAC,QAAQ;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAElC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACV,EAAA,CA1DQD,GAAG;EAAA,QACiClB,OAAO;AAAA;AAAAyC,EAAA,GAD3CvB,GAAG;AA4DZ,eAAeA,GAAG;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}