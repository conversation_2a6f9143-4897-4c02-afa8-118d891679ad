{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CollegamentiCavo.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, TextField, List, ListItem, ListItemText, Divider, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, FormControlLabel, Radio, RadioGroup, CircularProgress, Alert, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CollegamentiCavo = ({\n  cantiereId,\n  selectedCavo = null,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [cavi, setCavi] = useState([]);\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [internalSelectedCavo, setInternalSelectedCavo] = useState(selectedCavo);\n  const [openDialog, setOpenDialog] = useState(!!selectedCavo);\n  const [formData, setFormData] = useState({\n    lato: 'partenza',\n    responsabile: 'cantiere'\n  });\n\n  // Carica i cavi installati\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      console.log(`Caricamento cavi installati per cantiere ${cantiereId}...`);\n\n      // Ottieni solo i cavi installati\n      const response = await caviService.getCaviInstallati(cantiereId);\n      if (response && Array.isArray(response)) {\n        console.log(`Ricevuti ${response.length} cavi installati`);\n        setCavi(response);\n        setFilteredCavi(response);\n        if (response.length === 0) {\n          console.log('Nessun cavo installato trovato per questo cantiere');\n          // Non mostriamo un errore qui, l'interfaccia mostrerà già un messaggio appropriato\n        }\n      } else {\n        console.error('Risposta non valida dal server:', response);\n        setCavi([]);\n        setFilteredCavi([]);\n        onError('Formato risposta non valido dal server. Contattare l\\'amministratore.');\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      setCavi([]);\n      setFilteredCavi([]);\n\n      // Messaggio di errore più dettagliato e user-friendly\n      let errorMessage = 'Errore nel caricamento dei cavi: ';\n      if (error.detail) {\n        errorMessage += error.detail;\n      } else if (error.message) {\n        // Rimuovi dettagli tecnici dal messaggio di errore\n        const cleanMessage = error.message.replace(/http:\\/\\/localhost:\\d+/g, 'server').replace(/network error/i, 'errore di connessione');\n        errorMessage += cleanMessage;\n      } else {\n        errorMessage += 'Errore sconosciuto';\n      }\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const handleSearch = event => {\n    const term = event.target.value;\n    setSearchTerm(term);\n    if (!term.trim()) {\n      setFilteredCavi(cavi);\n    } else {\n      const filtered = cavi.filter(cavo => cavo.id_cavo.toLowerCase().includes(term.toLowerCase()));\n      setFilteredCavi(filtered);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setInternalSelectedCavo(cavo);\n    setOpenDialog(true);\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce il salvataggio del collegamento\n  const handleSaveCollegamento = async () => {\n    try {\n      setLoading(true);\n\n      // Determina se il lato è già collegato\n      const collegamenti = selectedCavo.collegamenti || 0;\n      const latoPartenzaCollegato = (collegamenti & 1) === 1;\n      const latoArrivoCollegato = (collegamenti & 2) === 2;\n\n      // Gestione delle operazioni basata sul valore selezionato\n      if (formData.lato === 'entrambi') {\n        // Collega entrambi i lati (solo quelli non ancora collegati)\n        let operazioniEffettuate = [];\n        if (!latoPartenzaCollegato) {\n          await caviService.collegaCavo(cantiereId, selectedCavo.id_cavo, 'partenza', formData.responsabile);\n          operazioniEffettuate.push('Lato partenza collegato');\n        }\n        if (!latoArrivoCollegato) {\n          await caviService.collegaCavo(cantiereId, selectedCavo.id_cavo, 'arrivo', formData.responsabile);\n          operazioniEffettuate.push('Lato arrivo collegato');\n        }\n        if (operazioniEffettuate.length > 0) {\n          onSuccess(`Cavo ${selectedCavo.id_cavo}: ${operazioniEffettuate.join(', ')}`);\n        } else {\n          onSuccess(`Cavo ${selectedCavo.id_cavo}: Entrambi i lati erano già collegati`);\n        }\n      } else if (formData.lato === 'scollega-entrambi') {\n        // Scollega entrambi i lati\n        await caviService.scollegaCavo(cantiereId, selectedCavo.id_cavo, 'partenza');\n        await caviService.scollegaCavo(cantiereId, selectedCavo.id_cavo, 'arrivo');\n        onSuccess(`Cavo ${selectedCavo.id_cavo}: Entrambi i lati scollegati`);\n      } else if (formData.lato === 'scollega-partenza') {\n        // Scollega solo il lato partenza\n        await caviService.scollegaCavo(cantiereId, selectedCavo.id_cavo, 'partenza');\n        onSuccess(`Cavo ${selectedCavo.id_cavo}: Lato partenza scollegato`);\n      } else if (formData.lato === 'scollega-arrivo') {\n        // Scollega solo il lato arrivo\n        await caviService.scollegaCavo(cantiereId, selectedCavo.id_cavo, 'arrivo');\n        onSuccess(`Cavo ${selectedCavo.id_cavo}: Lato arrivo scollegato`);\n      } else if (formData.lato === 'partenza') {\n        // Collega il lato partenza\n        await caviService.collegaCavo(cantiereId, selectedCavo.id_cavo, 'partenza', formData.responsabile);\n        onSuccess(`Cavo ${selectedCavo.id_cavo}: Lato partenza collegato`);\n      } else if (formData.lato === 'arrivo') {\n        // Collega il lato arrivo\n        await caviService.collegaCavo(cantiereId, selectedCavo.id_cavo, 'arrivo', formData.responsabile);\n        onSuccess(`Cavo ${selectedCavo.id_cavo}: Lato arrivo collegato`);\n      }\n\n      // Non serve più ricaricare i cavi o gestire dialog interni\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Formatta lo stato dei collegamenti\n  const formatStatoCollegamenti = collegamenti => {\n    collegamenti = collegamenti || 0;\n    if (collegamenti === 0) return \"Non collegato\";\n    if (collegamenti === 1) return \"Solo partenza\";\n    if (collegamenti === 2) return \"Solo arrivo\";\n    if (collegamenti === 3) return \"Completo\";\n    return `Sconosciuto (${collegamenti})`;\n  };\n\n  // Formatta lo stato di un singolo lato\n  const formatStatoLato = (collegamenti, lato) => {\n    collegamenti = collegamenti || 0;\n    if (lato === 'partenza') {\n      return collegamenti & 1 ? \"Collegato\" : \"Non collegato\";\n    } else {\n      return collegamenti & 2 ? \"Collegato\" : \"Non collegato\";\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        gutterBottom: true,\n        children: \"Informazioni Cavo:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [\"Partenza (FROM): \", selectedCavo.ubicazione_partenza || 'N/A']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [\"Arrivo (TO): \", selectedCavo.ubicazione_arrivo || 'N/A']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        gutterBottom: true,\n        children: \"Stato Collegamenti:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [\"Lato Partenza: \", formatStatoLato(selectedCavo.collegamenti, 'partenza'), selectedCavo.collegamenti & 1 ? ` (Responsabile: ${selectedCavo.responsabile_partenza || 'N/A'})` : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [\"Lato Arrivo: \", formatStatoLato(selectedCavo.collegamenti, 'arrivo'), selectedCavo.collegamenti & 2 ? ` (Responsabile: ${selectedCavo.responsabile_arrivo || 'N/A'})` : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        gutterBottom: true,\n        children: \"Gestisci Collegamenti:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        component: \"fieldset\",\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          gutterBottom: true,\n          children: \"Seleziona l'operazione da eseguire:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n          name: \"lato\",\n          value: formData.lato,\n          onChange: handleFormChange,\n          row: true,\n          children: (() => {\n            const collegamenti = (selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.collegamenti) || 0;\n            const latoPartenzaCollegato = (collegamenti & 1) === 1;\n            const latoArrivoCollegato = (collegamenti & 2) === 2;\n            const opzioni = [];\n\n            // Logica intelligente basata sullo stato attuale\n            if (collegamenti === 0) {\n              // Nessun collegamento - mostra opzioni per collegare\n              opzioni.push(/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"partenza\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 32\n                }, this),\n                label: \"\\uD83D\\uDFE2\\u26AA Collega solo lato partenza\"\n              }, \"collega-partenza\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 21\n              }, this));\n              opzioni.push(/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"arrivo\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 32\n                }, this),\n                label: \"\\u26AA\\uD83D\\uDFE2 Collega solo lato arrivo\"\n              }, \"collega-arrivo\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 21\n              }, this));\n              opzioni.push(/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"entrambi\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 32\n                }, this),\n                label: \"\\uD83D\\uDFE2\\uD83D\\uDFE2 Collega entrambi i lati\"\n              }, \"collega-entrambi\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this));\n            } else if (collegamenti === 1) {\n              // Solo partenza collegata - mostra opzioni per completare o scollegare\n              opzioni.push(/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"arrivo\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 32\n                }, this),\n                label: \"\\uD83D\\uDFE2\\uD83D\\uDFE2 Completa collegamento (collega arrivo)\"\n              }, \"collega-arrivo\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 21\n              }, this));\n              opzioni.push(/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"scollega-partenza\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 32\n                }, this),\n                label: \"\\u26AA\\u26AA Scollega lato partenza\"\n              }, \"scollega-partenza\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this));\n            } else if (collegamenti === 2) {\n              // Solo arrivo collegato - mostra opzioni per completare o scollegare\n              opzioni.push(/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"partenza\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 32\n                }, this),\n                label: \"\\uD83D\\uDFE2\\uD83D\\uDFE2 Completa collegamento (collega partenza)\"\n              }, \"collega-partenza\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 21\n              }, this));\n              opzioni.push(/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"scollega-arrivo\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 32\n                }, this),\n                label: \"\\u26AA\\u26AA Scollega lato arrivo\"\n              }, \"scollega-arrivo\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 21\n              }, this));\n            } else if (collegamenti === 3) {\n              // Entrambi collegati - mostra opzioni per scollegare\n              opzioni.push(/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"scollega-partenza\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 32\n                }, this),\n                label: \"\\u26AA\\uD83D\\uDFE2 Scollega solo lato partenza\"\n              }, \"scollega-partenza\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 21\n              }, this));\n              opzioni.push(/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"scollega-arrivo\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 32\n                }, this),\n                label: \"\\uD83D\\uDFE2\\u26AA Scollega solo lato arrivo\"\n              }, \"scollega-arrivo\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 21\n              }, this));\n              opzioni.push(/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"scollega-entrambi\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 32\n                }, this),\n                label: \"\\u26AA\\u26AA Scollega entrambi i lati\"\n              }, \"scollega-entrambi\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 21\n              }, this));\n            }\n            return opzioni;\n          })()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        margin: \"dense\",\n        name: \"responsabile\",\n        label: \"Responsabile del collegamento\",\n        fullWidth: true,\n        variant: \"outlined\",\n        value: formData.responsabile,\n        onChange: handleFormChange,\n        sx: {\n          mt: 2\n        },\n        helperText: \"Lascia vuoto per usare 'cantiere' come valore predefinito\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"primary\",\n        sx: {\n          mt: 2\n        },\n        children: (() => {\n          const collegamenti = selectedCavo.collegamenti || 0;\n          const latoPartenzaCollegato = (collegamenti & 1) === 1;\n          const latoArrivoCollegato = (collegamenti & 2) === 2;\n          switch (formData.lato) {\n            case 'entrambi':\n              return \"🟢🟢 Entrambi i lati verranno collegati.\";\n            case 'scollega-entrambi':\n              return \"⚪⚪ Entrambi i lati verranno scollegati.\";\n            case 'scollega-partenza':\n              return \"⚪🟢 Il lato partenza verrà scollegato.\";\n            case 'scollega-arrivo':\n              return \"🟢⚪ Il lato arrivo verrà scollegato.\";\n            case 'partenza':\n              return collegamenti === 2 ? \"🟢🟢 Il collegamento verrà completato (partenza).\" : \"🟢⚪ Il lato partenza verrà collegato.\";\n            case 'arrivo':\n              return collegamenti === 1 ? \"🟢🟢 Il collegamento verrà completato (arrivo).\" : \"⚪🟢 Il lato arrivo verrà collegato.\";\n            default:\n              return \"Seleziona un'operazione per vedere l'anteprima.\";\n          }\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSaveCollegamento,\n          disabled: loading || !selectedCavo || !formData.lato,\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"large\",\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 26\n          }, this) : (() => {\n            switch (formData.lato) {\n              case 'entrambi':\n                return \"Collega entrambi\";\n              case 'scollega-entrambi':\n                return \"Scollega entrambi\";\n              case 'scollega-partenza':\n                return \"Scollega partenza\";\n              case 'scollega-arrivo':\n                return \"Scollega arrivo\";\n              case 'partenza':\n                return \"Collega partenza\";\n              case 'arrivo':\n                return \"Collega arrivo\";\n              default:\n                return \"Seleziona un'operazione\";\n            }\n          })()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 9\n    }, this) :\n    /*#__PURE__*/\n    /* Mostra la ricerca e lista solo se non c'è un cavo preselezionato */\n    _jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        paragraph: true,\n        children: \"Visualizza e gestisci i collegamenti dei cavi installati.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        label: \"Cerca cavo per ID\",\n        variant: \"outlined\",\n        fullWidth: true,\n        margin: \"normal\",\n        value: searchTerm,\n        onChange: handleSearch,\n        placeholder: \"Inserisci l'ID del cavo da cercare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: loadCavi,\n        disabled: loading,\n        sx: {\n          mt: 2,\n          mb: 3\n        },\n        children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 24\n        }, this) : \"Aggiorna lista\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 11\n      }, this) : filteredCavi.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: searchTerm ? `Nessun cavo installato trovato con ID contenente \"${searchTerm}\".` : \"Nessun cavo installato trovato in questo cantiere.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 2,\n            mb: 1\n          },\n          children: \"Possibili motivi:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Non ci sono cavi nello stato \\\"INSTALLATO\\\" in questo cantiere.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Il cavo che stai cercando potrebbe essere in uno stato diverso da \\\"INSTALLATO\\\" (es. \\\"DA INSTALLARE\\\", \\\"POSATO\\\").\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Il cavo potrebbe essere marcato come SPARE.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 2\n          },\n          children: \"Suggerimenti:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Verifica lo stato del cavo nella pagina di gestione cavi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Assicurati che il cavo sia stato installato correttamente.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Lato Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Lato Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Resp. Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Resp. Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: filteredCavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatStatoCollegamenti(cavo.collegamenti)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatStatoLato(cavo.collegamenti, 'partenza')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatStatoLato(cavo.collegamenti, 'arrivo')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.responsabile_partenza || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.responsabile_arrivo || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  onClick: () => handleCavoSelect(cavo),\n                  children: \"Gestisci\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n};\n_s(CollegamentiCavo, \"KRTAOdb+Gkj+YGFNCM8lcI7XxAM=\");\n_c = CollegamentiCavo;\nexport default CollegamentiCavo;\nvar _c;\n$RefreshReg$(_c, \"CollegamentiCavo\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "TextField", "List", "ListItem", "ListItemText", "Divider", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "FormControlLabel", "Radio", "RadioGroup", "CircularProgress", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "caviService", "jsxDEV", "_jsxDEV", "CollegamentiCavo", "cantiereId", "selected<PERSON><PERSON><PERSON>", "onSuccess", "onError", "_s", "loading", "setLoading", "cavi", "<PERSON><PERSON><PERSON>", "filteredCavi", "setFilteredCavi", "searchTerm", "setSearchTerm", "internalSelectedCavo", "setInternalSelectedCavo", "openDialog", "setOpenDialog", "formData", "setFormData", "lato", "responsabile", "loadCavi", "console", "log", "response", "getCaviInstallati", "Array", "isArray", "length", "error", "errorMessage", "detail", "message", "cleanMessage", "replace", "handleSearch", "event", "term", "target", "value", "trim", "filtered", "filter", "cavo", "id_cavo", "toLowerCase", "includes", "handleCavoSelect", "handleFormChange", "e", "name", "handleSaveCollegamento", "colle<PERSON>nti", "latoPartenzaCollegato", "latoArrivoCollegato", "operazioniEffettuate", "collegaCavo", "push", "join", "scollegaCavo", "formatStatoCollegamenti", "formatStatoLato", "children", "sx", "mt", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "my", "ubicazione_partenza", "ubicazione_arrivo", "responsabile_partenza", "responsabile_arrivo", "component", "onChange", "row", "opzioni", "control", "label", "margin", "fullWidth", "helperText", "color", "display", "justifyContent", "onClick", "disabled", "size", "p", "mb", "paragraph", "placeholder", "severity", "map", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CollegamentiCavo.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  TextField,\n  List,\n  ListItem,\n  ListItemText,\n  Divider,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  FormControlLabel,\n  Radio,\n  RadioGroup,\n  CircularProgress,\n  Alert,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow\n} from '@mui/material';\nimport caviService from '../../services/caviService';\n\nconst CollegamentiCavo = ({ cantiereId, selectedCavo = null, onSuccess, onError }) => {\n  const [loading, setLoading] = useState(false);\n  const [cavi, setCavi] = useState([]);\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [internalSelectedCavo, setInternalSelectedCavo] = useState(selectedCavo);\n  const [openDialog, setOpenDialog] = useState(!!selectedCavo);\n  const [formData, setFormData] = useState({\n    lato: 'partenza',\n    responsabile: 'cantiere'\n  });\n\n  // Carica i cavi installati\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      console.log(`Caricamento cavi installati per cantiere ${cantiereId}...`);\n\n      // Ottieni solo i cavi installati\n      const response = await caviService.getCaviInstallati(cantiereId);\n\n      if (response && Array.isArray(response)) {\n        console.log(`Ricevuti ${response.length} cavi installati`);\n        setCavi(response);\n        setFilteredCavi(response);\n\n        if (response.length === 0) {\n          console.log('Nessun cavo installato trovato per questo cantiere');\n          // Non mostriamo un errore qui, l'interfaccia mostrerà già un messaggio appropriato\n        }\n      } else {\n        console.error('Risposta non valida dal server:', response);\n        setCavi([]);\n        setFilteredCavi([]);\n        onError('Formato risposta non valido dal server. Contattare l\\'amministratore.');\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      setCavi([]);\n      setFilteredCavi([]);\n\n      // Messaggio di errore più dettagliato e user-friendly\n      let errorMessage = 'Errore nel caricamento dei cavi: ';\n\n      if (error.detail) {\n        errorMessage += error.detail;\n      } else if (error.message) {\n        // Rimuovi dettagli tecnici dal messaggio di errore\n        const cleanMessage = error.message\n          .replace(/http:\\/\\/localhost:\\d+/g, 'server')\n          .replace(/network error/i, 'errore di connessione');\n        errorMessage += cleanMessage;\n      } else {\n        errorMessage += 'Errore sconosciuto';\n      }\n\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const handleSearch = (event) => {\n    const term = event.target.value;\n    setSearchTerm(term);\n\n    if (!term.trim()) {\n      setFilteredCavi(cavi);\n    } else {\n      const filtered = cavi.filter(cavo => \n        cavo.id_cavo.toLowerCase().includes(term.toLowerCase())\n      );\n      setFilteredCavi(filtered);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setInternalSelectedCavo(cavo);\n    setOpenDialog(true);\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce il salvataggio del collegamento\n  const handleSaveCollegamento = async () => {\n    try {\n      setLoading(true);\n\n      // Determina se il lato è già collegato\n      const collegamenti = selectedCavo.collegamenti || 0;\n      const latoPartenzaCollegato = (collegamenti & 1) === 1;\n      const latoArrivoCollegato = (collegamenti & 2) === 2;\n\n      // Gestione delle operazioni basata sul valore selezionato\n      if (formData.lato === 'entrambi') {\n        // Collega entrambi i lati (solo quelli non ancora collegati)\n        let operazioniEffettuate = [];\n\n        if (!latoPartenzaCollegato) {\n          await caviService.collegaCavo(cantiereId, selectedCavo.id_cavo, 'partenza', formData.responsabile);\n          operazioniEffettuate.push('Lato partenza collegato');\n        }\n\n        if (!latoArrivoCollegato) {\n          await caviService.collegaCavo(cantiereId, selectedCavo.id_cavo, 'arrivo', formData.responsabile);\n          operazioniEffettuate.push('Lato arrivo collegato');\n        }\n\n        if (operazioniEffettuate.length > 0) {\n          onSuccess(`Cavo ${selectedCavo.id_cavo}: ${operazioniEffettuate.join(', ')}`);\n        } else {\n          onSuccess(`Cavo ${selectedCavo.id_cavo}: Entrambi i lati erano già collegati`);\n        }\n      } else if (formData.lato === 'scollega-entrambi') {\n        // Scollega entrambi i lati\n        await caviService.scollegaCavo(cantiereId, selectedCavo.id_cavo, 'partenza');\n        await caviService.scollegaCavo(cantiereId, selectedCavo.id_cavo, 'arrivo');\n        onSuccess(`Cavo ${selectedCavo.id_cavo}: Entrambi i lati scollegati`);\n      } else if (formData.lato === 'scollega-partenza') {\n        // Scollega solo il lato partenza\n        await caviService.scollegaCavo(cantiereId, selectedCavo.id_cavo, 'partenza');\n        onSuccess(`Cavo ${selectedCavo.id_cavo}: Lato partenza scollegato`);\n      } else if (formData.lato === 'scollega-arrivo') {\n        // Scollega solo il lato arrivo\n        await caviService.scollegaCavo(cantiereId, selectedCavo.id_cavo, 'arrivo');\n        onSuccess(`Cavo ${selectedCavo.id_cavo}: Lato arrivo scollegato`);\n      } else if (formData.lato === 'partenza') {\n        // Collega il lato partenza\n        await caviService.collegaCavo(cantiereId, selectedCavo.id_cavo, 'partenza', formData.responsabile);\n        onSuccess(`Cavo ${selectedCavo.id_cavo}: Lato partenza collegato`);\n      } else if (formData.lato === 'arrivo') {\n        // Collega il lato arrivo\n        await caviService.collegaCavo(cantiereId, selectedCavo.id_cavo, 'arrivo', formData.responsabile);\n        onSuccess(`Cavo ${selectedCavo.id_cavo}: Lato arrivo collegato`);\n      }\n\n      // Non serve più ricaricare i cavi o gestire dialog interni\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Formatta lo stato dei collegamenti\n  const formatStatoCollegamenti = (collegamenti) => {\n    collegamenti = collegamenti || 0;\n\n    if (collegamenti === 0) return \"Non collegato\";\n    if (collegamenti === 1) return \"Solo partenza\";\n    if (collegamenti === 2) return \"Solo arrivo\";\n    if (collegamenti === 3) return \"Completo\";\n    return `Sconosciuto (${collegamenti})`;\n  };\n\n  // Formatta lo stato di un singolo lato\n  const formatStatoLato = (collegamenti, lato) => {\n    collegamenti = collegamenti || 0;\n\n    if (lato === 'partenza') {\n      return (collegamenti & 1) ? \"Collegato\" : \"Non collegato\";\n    } else {\n      return (collegamenti & 2) ? \"Collegato\" : \"Non collegato\";\n    }\n  };\n\n  return (\n    <Box>\n      {/* Se c'è un cavo preselezionato, mostra direttamente il form di gestione */}\n      {selectedCavo ? (\n        <Box sx={{ mt: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Cavo selezionato: {selectedCavo.id_cavo}\n          </Typography>\n\n          <Divider sx={{ my: 2 }} />\n\n          <Typography variant=\"subtitle2\" gutterBottom>\n            Informazioni Cavo:\n          </Typography>\n          <Typography variant=\"body2\">\n            Partenza (FROM): {selectedCavo.ubicazione_partenza || 'N/A'}\n          </Typography>\n          <Typography variant=\"body2\">\n            Arrivo (TO): {selectedCavo.ubicazione_arrivo || 'N/A'}\n          </Typography>\n\n          <Divider sx={{ my: 2 }} />\n\n          <Typography variant=\"subtitle2\" gutterBottom>\n            Stato Collegamenti:\n          </Typography>\n          <Typography variant=\"body2\">\n            Lato Partenza: {formatStatoLato(selectedCavo.collegamenti, 'partenza')}\n            {(selectedCavo.collegamenti & 1) ? ` (Responsabile: ${selectedCavo.responsabile_partenza || 'N/A'})` : ''}\n          </Typography>\n          <Typography variant=\"body2\">\n            Lato Arrivo: {formatStatoLato(selectedCavo.collegamenti, 'arrivo')}\n            {(selectedCavo.collegamenti & 2) ? ` (Responsabile: ${selectedCavo.responsabile_arrivo || 'N/A'})` : ''}\n          </Typography>\n\n          <Divider sx={{ my: 2 }} />\n\n          <Typography variant=\"subtitle2\" gutterBottom>\n            Gestisci Collegamenti:\n          </Typography>\n\n          <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" gutterBottom>\n              Seleziona l'operazione da eseguire:\n            </Typography>\n            <RadioGroup\n              name=\"lato\"\n              value={formData.lato}\n              onChange={handleFormChange}\n              row\n            >\n              {/* Mostra opzioni intelligenti basate sullo stato del cavo */}\n              {(() => {\n                const collegamenti = selectedCavo?.collegamenti || 0;\n                const latoPartenzaCollegato = (collegamenti & 1) === 1;\n                const latoArrivoCollegato = (collegamenti & 2) === 2;\n\n                const opzioni = [];\n\n                // Logica intelligente basata sullo stato attuale\n                if (collegamenti === 0) {\n                  // Nessun collegamento - mostra opzioni per collegare\n                  opzioni.push(\n                    <FormControlLabel\n                      key=\"collega-partenza\"\n                      value=\"partenza\"\n                      control={<Radio />}\n                      label=\"🟢⚪ Collega solo lato partenza\"\n                    />\n                  );\n                  opzioni.push(\n                    <FormControlLabel\n                      key=\"collega-arrivo\"\n                      value=\"arrivo\"\n                      control={<Radio />}\n                      label=\"⚪🟢 Collega solo lato arrivo\"\n                    />\n                  );\n                  opzioni.push(\n                    <FormControlLabel\n                      key=\"collega-entrambi\"\n                      value=\"entrambi\"\n                      control={<Radio />}\n                      label=\"🟢🟢 Collega entrambi i lati\"\n                    />\n                  );\n                } else if (collegamenti === 1) {\n                  // Solo partenza collegata - mostra opzioni per completare o scollegare\n                  opzioni.push(\n                    <FormControlLabel\n                      key=\"collega-arrivo\"\n                      value=\"arrivo\"\n                      control={<Radio />}\n                      label=\"🟢🟢 Completa collegamento (collega arrivo)\"\n                    />\n                  );\n                  opzioni.push(\n                    <FormControlLabel\n                      key=\"scollega-partenza\"\n                      value=\"scollega-partenza\"\n                      control={<Radio />}\n                      label=\"⚪⚪ Scollega lato partenza\"\n                    />\n                  );\n                } else if (collegamenti === 2) {\n                  // Solo arrivo collegato - mostra opzioni per completare o scollegare\n                  opzioni.push(\n                    <FormControlLabel\n                      key=\"collega-partenza\"\n                      value=\"partenza\"\n                      control={<Radio />}\n                      label=\"🟢🟢 Completa collegamento (collega partenza)\"\n                    />\n                  );\n                  opzioni.push(\n                    <FormControlLabel\n                      key=\"scollega-arrivo\"\n                      value=\"scollega-arrivo\"\n                      control={<Radio />}\n                      label=\"⚪⚪ Scollega lato arrivo\"\n                    />\n                  );\n                } else if (collegamenti === 3) {\n                  // Entrambi collegati - mostra opzioni per scollegare\n                  opzioni.push(\n                    <FormControlLabel\n                      key=\"scollega-partenza\"\n                      value=\"scollega-partenza\"\n                      control={<Radio />}\n                      label=\"⚪🟢 Scollega solo lato partenza\"\n                    />\n                  );\n                  opzioni.push(\n                    <FormControlLabel\n                      key=\"scollega-arrivo\"\n                      value=\"scollega-arrivo\"\n                      control={<Radio />}\n                      label=\"🟢⚪ Scollega solo lato arrivo\"\n                    />\n                  );\n                  opzioni.push(\n                    <FormControlLabel\n                      key=\"scollega-entrambi\"\n                      value=\"scollega-entrambi\"\n                      control={<Radio />}\n                      label=\"⚪⚪ Scollega entrambi i lati\"\n                    />\n                  );\n                }\n\n                return opzioni;\n              })()}\n            </RadioGroup>\n          </FormControl>\n\n          <TextField\n            margin=\"dense\"\n            name=\"responsabile\"\n            label=\"Responsabile del collegamento\"\n            fullWidth\n            variant=\"outlined\"\n            value={formData.responsabile}\n            onChange={handleFormChange}\n            sx={{ mt: 2 }}\n            helperText=\"Lascia vuoto per usare 'cantiere' come valore predefinito\"\n          />\n\n          <Typography variant=\"body2\" color=\"primary\" sx={{ mt: 2 }}>\n            {(() => {\n              const collegamenti = selectedCavo.collegamenti || 0;\n              const latoPartenzaCollegato = (collegamenti & 1) === 1;\n              const latoArrivoCollegato = (collegamenti & 2) === 2;\n\n              switch (formData.lato) {\n                case 'entrambi':\n                  return \"🟢🟢 Entrambi i lati verranno collegati.\";\n                case 'scollega-entrambi':\n                  return \"⚪⚪ Entrambi i lati verranno scollegati.\";\n                case 'scollega-partenza':\n                  return \"⚪🟢 Il lato partenza verrà scollegato.\";\n                case 'scollega-arrivo':\n                  return \"🟢⚪ Il lato arrivo verrà scollegato.\";\n                case 'partenza':\n                  return collegamenti === 2 ? \"🟢🟢 Il collegamento verrà completato (partenza).\" : \"🟢⚪ Il lato partenza verrà collegato.\";\n                case 'arrivo':\n                  return collegamenti === 1 ? \"🟢🟢 Il collegamento verrà completato (arrivo).\" : \"⚪🟢 Il lato arrivo verrà collegato.\";\n                default:\n                  return \"Seleziona un'operazione per vedere l'anteprima.\";\n              }\n            })()}\n          </Typography>\n\n          {/* Pulsante di azione */}\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>\n            <Button\n              onClick={handleSaveCollegamento}\n              disabled={loading || !selectedCavo || !formData.lato}\n              variant=\"contained\"\n              color=\"primary\"\n              size=\"large\"\n            >\n              {loading ? <CircularProgress size={24} /> :\n                (() => {\n                  switch (formData.lato) {\n                    case 'entrambi':\n                      return \"Collega entrambi\";\n                    case 'scollega-entrambi':\n                      return \"Scollega entrambi\";\n                    case 'scollega-partenza':\n                      return \"Scollega partenza\";\n                    case 'scollega-arrivo':\n                      return \"Scollega arrivo\";\n                    case 'partenza':\n                      return \"Collega partenza\";\n                    case 'arrivo':\n                      return \"Collega arrivo\";\n                    default:\n                      return \"Seleziona un'operazione\";\n                  }\n                })()}\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        /* Mostra la ricerca e lista solo se non c'è un cavo preselezionato */\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"body2\" paragraph>\n            Visualizza e gestisci i collegamenti dei cavi installati.\n          </Typography>\n\n          <TextField\n            label=\"Cerca cavo per ID\"\n            variant=\"outlined\"\n            fullWidth\n            margin=\"normal\"\n            value={searchTerm}\n            onChange={handleSearch}\n            placeholder=\"Inserisci l'ID del cavo da cercare\"\n          />\n\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={loadCavi}\n            disabled={loading}\n            sx={{ mt: 2, mb: 3 }}\n          >\n            {loading ? <CircularProgress size={24} /> : \"Aggiorna lista\"}\n          </Button>\n\n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n            <CircularProgress />\n          </Box>\n        ) : filteredCavi.length === 0 ? (\n          <Box sx={{ mt: 2 }}>\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              {searchTerm ? \n                `Nessun cavo installato trovato con ID contenente \"${searchTerm}\".` : \n                \"Nessun cavo installato trovato in questo cantiere.\"}\n            </Alert>\n            <Typography variant=\"body2\" sx={{ mt: 2, mb: 1 }}>\n              Possibili motivi:\n            </Typography>\n            <ul>\n              <li>\n                <Typography variant=\"body2\">\n                  Non ci sono cavi nello stato \"INSTALLATO\" in questo cantiere.\n                </Typography>\n              </li>\n              <li>\n                <Typography variant=\"body2\">\n                  Il cavo che stai cercando potrebbe essere in uno stato diverso da \"INSTALLATO\" (es. \"DA INSTALLARE\", \"POSATO\").\n                </Typography>\n              </li>\n              <li>\n                <Typography variant=\"body2\">\n                  Il cavo potrebbe essere marcato come SPARE.\n                </Typography>\n              </li>\n            </ul>\n            <Typography variant=\"body2\" sx={{ mt: 2 }}>\n              Suggerimenti:\n            </Typography>\n            <ul>\n              <li>\n                <Typography variant=\"body2\">\n                  Verifica lo stato del cavo nella pagina di gestione cavi.\n                </Typography>\n              </li>\n              <li>\n                <Typography variant=\"body2\">\n                  Assicurati che il cavo sia stato installato correttamente.\n                </Typography>\n              </li>\n            </ul>\n          </Box>\n        ) : (\n          <TableContainer component={Paper} sx={{ mt: 2 }}>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell>Lato Partenza</TableCell>\n                  <TableCell>Lato Arrivo</TableCell>\n                  <TableCell>Resp. Partenza</TableCell>\n                  <TableCell>Resp. Arrivo</TableCell>\n                  <TableCell>Azioni</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {filteredCavi.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{cavo.id_cavo}</TableCell>\n                    <TableCell>{formatStatoCollegamenti(cavo.collegamenti)}</TableCell>\n                    <TableCell>{formatStatoLato(cavo.collegamenti, 'partenza')}</TableCell>\n                    <TableCell>{formatStatoLato(cavo.collegamenti, 'arrivo')}</TableCell>\n                    <TableCell>{cavo.responsabile_partenza || '-'}</TableCell>\n                    <TableCell>{cavo.responsabile_arrivo || '-'}</TableCell>\n                    <TableCell>\n                      <Button\n                        variant=\"outlined\"\n                        size=\"small\"\n                        onClick={() => handleCavoSelect(cavo)}\n                      >\n                        Gestisci\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        )}\n        </Paper>\n      )}\n    </Box>\n  );\n};\n\nexport default CollegamentiCavo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,gBAAgB,EAChBC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,QACH,eAAe;AACtB,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,YAAY,GAAG,IAAI;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,IAAI,EAAEC,OAAO,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7C,QAAQ,CAACgC,YAAY,CAAC;EAC9E,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAACgC,YAAY,CAAC;EAC5D,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC;IACvCkD,IAAI,EAAE,UAAU;IAChBC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACAlD,SAAS,CAAC,MAAM;IACdmD,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACrB,UAAU,CAAC,CAAC;EAEhB,MAAMqB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChBgB,OAAO,CAACC,GAAG,CAAC,4CAA4CvB,UAAU,KAAK,CAAC;;MAExE;MACA,MAAMwB,QAAQ,GAAG,MAAM5B,WAAW,CAAC6B,iBAAiB,CAACzB,UAAU,CAAC;MAEhE,IAAIwB,QAAQ,IAAIE,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;QACvCF,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACI,MAAM,kBAAkB,CAAC;QAC1DpB,OAAO,CAACgB,QAAQ,CAAC;QACjBd,eAAe,CAACc,QAAQ,CAAC;QAEzB,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;UACzBN,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE;QACF;MACF,CAAC,MAAM;QACLD,OAAO,CAACO,KAAK,CAAC,iCAAiC,EAAEL,QAAQ,CAAC;QAC1DhB,OAAO,CAAC,EAAE,CAAC;QACXE,eAAe,CAAC,EAAE,CAAC;QACnBP,OAAO,CAAC,uEAAuE,CAAC;MAClF;IACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDrB,OAAO,CAAC,EAAE,CAAC;MACXE,eAAe,CAAC,EAAE,CAAC;;MAEnB;MACA,IAAIoB,YAAY,GAAG,mCAAmC;MAEtD,IAAID,KAAK,CAACE,MAAM,EAAE;QAChBD,YAAY,IAAID,KAAK,CAACE,MAAM;MAC9B,CAAC,MAAM,IAAIF,KAAK,CAACG,OAAO,EAAE;QACxB;QACA,MAAMC,YAAY,GAAGJ,KAAK,CAACG,OAAO,CAC/BE,OAAO,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAC5CA,OAAO,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACrDJ,YAAY,IAAIG,YAAY;MAC9B,CAAC,MAAM;QACLH,YAAY,IAAI,oBAAoB;MACtC;MAEA3B,OAAO,CAAC2B,YAAY,CAAC;IACvB,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6B,YAAY,GAAIC,KAAK,IAAK;IAC9B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;IAC/B3B,aAAa,CAACyB,IAAI,CAAC;IAEnB,IAAI,CAACA,IAAI,CAACG,IAAI,CAAC,CAAC,EAAE;MAChB9B,eAAe,CAACH,IAAI,CAAC;IACvB,CAAC,MAAM;MACL,MAAMkC,QAAQ,GAAGlC,IAAI,CAACmC,MAAM,CAACC,IAAI,IAC/BA,IAAI,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,IAAI,CAACQ,WAAW,CAAC,CAAC,CACxD,CAAC;MACDnC,eAAe,CAAC+B,QAAQ,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMM,gBAAgB,GAAIJ,IAAI,IAAK;IACjC7B,uBAAuB,CAAC6B,IAAI,CAAC;IAC7B3B,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAMgC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEX;IAAM,CAAC,GAAGU,CAAC,CAACX,MAAM;IAChCpB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACiC,IAAI,GAAGX;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMY,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF7C,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM8C,YAAY,GAAGnD,YAAY,CAACmD,YAAY,IAAI,CAAC;MACnD,MAAMC,qBAAqB,GAAG,CAACD,YAAY,GAAG,CAAC,MAAM,CAAC;MACtD,MAAME,mBAAmB,GAAG,CAACF,YAAY,GAAG,CAAC,MAAM,CAAC;;MAEpD;MACA,IAAInC,QAAQ,CAACE,IAAI,KAAK,UAAU,EAAE;QAChC;QACA,IAAIoC,oBAAoB,GAAG,EAAE;QAE7B,IAAI,CAACF,qBAAqB,EAAE;UAC1B,MAAMzD,WAAW,CAAC4D,WAAW,CAACxD,UAAU,EAAEC,YAAY,CAAC2C,OAAO,EAAE,UAAU,EAAE3B,QAAQ,CAACG,YAAY,CAAC;UAClGmC,oBAAoB,CAACE,IAAI,CAAC,yBAAyB,CAAC;QACtD;QAEA,IAAI,CAACH,mBAAmB,EAAE;UACxB,MAAM1D,WAAW,CAAC4D,WAAW,CAACxD,UAAU,EAAEC,YAAY,CAAC2C,OAAO,EAAE,QAAQ,EAAE3B,QAAQ,CAACG,YAAY,CAAC;UAChGmC,oBAAoB,CAACE,IAAI,CAAC,uBAAuB,CAAC;QACpD;QAEA,IAAIF,oBAAoB,CAAC3B,MAAM,GAAG,CAAC,EAAE;UACnC1B,SAAS,CAAC,QAAQD,YAAY,CAAC2C,OAAO,KAAKW,oBAAoB,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/E,CAAC,MAAM;UACLxD,SAAS,CAAC,QAAQD,YAAY,CAAC2C,OAAO,uCAAuC,CAAC;QAChF;MACF,CAAC,MAAM,IAAI3B,QAAQ,CAACE,IAAI,KAAK,mBAAmB,EAAE;QAChD;QACA,MAAMvB,WAAW,CAAC+D,YAAY,CAAC3D,UAAU,EAAEC,YAAY,CAAC2C,OAAO,EAAE,UAAU,CAAC;QAC5E,MAAMhD,WAAW,CAAC+D,YAAY,CAAC3D,UAAU,EAAEC,YAAY,CAAC2C,OAAO,EAAE,QAAQ,CAAC;QAC1E1C,SAAS,CAAC,QAAQD,YAAY,CAAC2C,OAAO,8BAA8B,CAAC;MACvE,CAAC,MAAM,IAAI3B,QAAQ,CAACE,IAAI,KAAK,mBAAmB,EAAE;QAChD;QACA,MAAMvB,WAAW,CAAC+D,YAAY,CAAC3D,UAAU,EAAEC,YAAY,CAAC2C,OAAO,EAAE,UAAU,CAAC;QAC5E1C,SAAS,CAAC,QAAQD,YAAY,CAAC2C,OAAO,4BAA4B,CAAC;MACrE,CAAC,MAAM,IAAI3B,QAAQ,CAACE,IAAI,KAAK,iBAAiB,EAAE;QAC9C;QACA,MAAMvB,WAAW,CAAC+D,YAAY,CAAC3D,UAAU,EAAEC,YAAY,CAAC2C,OAAO,EAAE,QAAQ,CAAC;QAC1E1C,SAAS,CAAC,QAAQD,YAAY,CAAC2C,OAAO,0BAA0B,CAAC;MACnE,CAAC,MAAM,IAAI3B,QAAQ,CAACE,IAAI,KAAK,UAAU,EAAE;QACvC;QACA,MAAMvB,WAAW,CAAC4D,WAAW,CAACxD,UAAU,EAAEC,YAAY,CAAC2C,OAAO,EAAE,UAAU,EAAE3B,QAAQ,CAACG,YAAY,CAAC;QAClGlB,SAAS,CAAC,QAAQD,YAAY,CAAC2C,OAAO,2BAA2B,CAAC;MACpE,CAAC,MAAM,IAAI3B,QAAQ,CAACE,IAAI,KAAK,QAAQ,EAAE;QACrC;QACA,MAAMvB,WAAW,CAAC4D,WAAW,CAACxD,UAAU,EAAEC,YAAY,CAAC2C,OAAO,EAAE,QAAQ,EAAE3B,QAAQ,CAACG,YAAY,CAAC;QAChGlB,SAAS,CAAC,QAAQD,YAAY,CAAC2C,OAAO,yBAAyB,CAAC;MAClE;;MAEA;IACF,CAAC,CAAC,OAAOf,KAAK,EAAE;MACd1B,OAAO,CAAC,gCAAgC,IAAI0B,KAAK,CAACG,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACnFV,OAAO,CAACO,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsD,uBAAuB,GAAIR,YAAY,IAAK;IAChDA,YAAY,GAAGA,YAAY,IAAI,CAAC;IAEhC,IAAIA,YAAY,KAAK,CAAC,EAAE,OAAO,eAAe;IAC9C,IAAIA,YAAY,KAAK,CAAC,EAAE,OAAO,eAAe;IAC9C,IAAIA,YAAY,KAAK,CAAC,EAAE,OAAO,aAAa;IAC5C,IAAIA,YAAY,KAAK,CAAC,EAAE,OAAO,UAAU;IACzC,OAAO,gBAAgBA,YAAY,GAAG;EACxC,CAAC;;EAED;EACA,MAAMS,eAAe,GAAGA,CAACT,YAAY,EAAEjC,IAAI,KAAK;IAC9CiC,YAAY,GAAGA,YAAY,IAAI,CAAC;IAEhC,IAAIjC,IAAI,KAAK,UAAU,EAAE;MACvB,OAAQiC,YAAY,GAAG,CAAC,GAAI,WAAW,GAAG,eAAe;IAC3D,CAAC,MAAM;MACL,OAAQA,YAAY,GAAG,CAAC,GAAI,WAAW,GAAG,eAAe;IAC3D;EACF,CAAC;EAED,oBACEtD,OAAA,CAAC3B,GAAG;IAAA2F,QAAA,EAED7D,YAAY,gBACXH,OAAA,CAAC3B,GAAG;MAAC4F,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBACjBhE,OAAA,CAAC1B,UAAU;QAAC6F,OAAO,EAAC,WAAW;QAACC,YAAY;QAAAJ,QAAA,GAAC,oBACzB,EAAC7D,YAAY,CAAC2C,OAAO;MAAA;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEbxE,OAAA,CAACnB,OAAO;QAACoF,EAAE,EAAE;UAAEQ,EAAE,EAAE;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BxE,OAAA,CAAC1B,UAAU;QAAC6F,OAAO,EAAC,WAAW;QAACC,YAAY;QAAAJ,QAAA,EAAC;MAE7C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxE,OAAA,CAAC1B,UAAU;QAAC6F,OAAO,EAAC,OAAO;QAAAH,QAAA,GAAC,mBACT,EAAC7D,YAAY,CAACuE,mBAAmB,IAAI,KAAK;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACbxE,OAAA,CAAC1B,UAAU;QAAC6F,OAAO,EAAC,OAAO;QAAAH,QAAA,GAAC,eACb,EAAC7D,YAAY,CAACwE,iBAAiB,IAAI,KAAK;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAEbxE,OAAA,CAACnB,OAAO;QAACoF,EAAE,EAAE;UAAEQ,EAAE,EAAE;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BxE,OAAA,CAAC1B,UAAU;QAAC6F,OAAO,EAAC,WAAW;QAACC,YAAY;QAAAJ,QAAA,EAAC;MAE7C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxE,OAAA,CAAC1B,UAAU;QAAC6F,OAAO,EAAC,OAAO;QAAAH,QAAA,GAAC,iBACX,EAACD,eAAe,CAAC5D,YAAY,CAACmD,YAAY,EAAE,UAAU,CAAC,EACpEnD,YAAY,CAACmD,YAAY,GAAG,CAAC,GAAI,mBAAmBnD,YAAY,CAACyE,qBAAqB,IAAI,KAAK,GAAG,GAAG,EAAE;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC,eACbxE,OAAA,CAAC1B,UAAU;QAAC6F,OAAO,EAAC,OAAO;QAAAH,QAAA,GAAC,eACb,EAACD,eAAe,CAAC5D,YAAY,CAACmD,YAAY,EAAE,QAAQ,CAAC,EAChEnD,YAAY,CAACmD,YAAY,GAAG,CAAC,GAAI,mBAAmBnD,YAAY,CAAC0E,mBAAmB,IAAI,KAAK,GAAG,GAAG,EAAE;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F,CAAC,eAEbxE,OAAA,CAACnB,OAAO;QAACoF,EAAE,EAAE;UAAEQ,EAAE,EAAE;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BxE,OAAA,CAAC1B,UAAU;QAAC6F,OAAO,EAAC,WAAW;QAACC,YAAY;QAAAJ,QAAA,EAAC;MAE7C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbxE,OAAA,CAACd,WAAW;QAAC4F,SAAS,EAAC,UAAU;QAACb,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,gBAC9ChE,OAAA,CAAC1B,UAAU;UAAC6F,OAAO,EAAC,OAAO;UAACC,YAAY;UAAAJ,QAAA,EAAC;QAEzC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxE,OAAA,CAACX,UAAU;UACT+D,IAAI,EAAC,MAAM;UACXX,KAAK,EAAEtB,QAAQ,CAACE,IAAK;UACrB0D,QAAQ,EAAE7B,gBAAiB;UAC3B8B,GAAG;UAAAhB,QAAA,EAGF,CAAC,MAAM;YACN,MAAMV,YAAY,GAAG,CAAAnD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEmD,YAAY,KAAI,CAAC;YACpD,MAAMC,qBAAqB,GAAG,CAACD,YAAY,GAAG,CAAC,MAAM,CAAC;YACtD,MAAME,mBAAmB,GAAG,CAACF,YAAY,GAAG,CAAC,MAAM,CAAC;YAEpD,MAAM2B,OAAO,GAAG,EAAE;;YAElB;YACA,IAAI3B,YAAY,KAAK,CAAC,EAAE;cACtB;cACA2B,OAAO,CAACtB,IAAI,cACV3D,OAAA,CAACb,gBAAgB;gBAEfsD,KAAK,EAAC,UAAU;gBAChByC,OAAO,eAAElF,OAAA,CAACZ,KAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBW,KAAK,EAAC;cAAgC,GAHlC,kBAAkB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIvB,CACH,CAAC;cACDS,OAAO,CAACtB,IAAI,cACV3D,OAAA,CAACb,gBAAgB;gBAEfsD,KAAK,EAAC,QAAQ;gBACdyC,OAAO,eAAElF,OAAA,CAACZ,KAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBW,KAAK,EAAC;cAA8B,GAHhC,gBAAgB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIrB,CACH,CAAC;cACDS,OAAO,CAACtB,IAAI,cACV3D,OAAA,CAACb,gBAAgB;gBAEfsD,KAAK,EAAC,UAAU;gBAChByC,OAAO,eAAElF,OAAA,CAACZ,KAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBW,KAAK,EAAC;cAA8B,GAHhC,kBAAkB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIvB,CACH,CAAC;YACH,CAAC,MAAM,IAAIlB,YAAY,KAAK,CAAC,EAAE;cAC7B;cACA2B,OAAO,CAACtB,IAAI,cACV3D,OAAA,CAACb,gBAAgB;gBAEfsD,KAAK,EAAC,QAAQ;gBACdyC,OAAO,eAAElF,OAAA,CAACZ,KAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBW,KAAK,EAAC;cAA6C,GAH/C,gBAAgB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIrB,CACH,CAAC;cACDS,OAAO,CAACtB,IAAI,cACV3D,OAAA,CAACb,gBAAgB;gBAEfsD,KAAK,EAAC,mBAAmB;gBACzByC,OAAO,eAAElF,OAAA,CAACZ,KAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBW,KAAK,EAAC;cAA2B,GAH7B,mBAAmB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIxB,CACH,CAAC;YACH,CAAC,MAAM,IAAIlB,YAAY,KAAK,CAAC,EAAE;cAC7B;cACA2B,OAAO,CAACtB,IAAI,cACV3D,OAAA,CAACb,gBAAgB;gBAEfsD,KAAK,EAAC,UAAU;gBAChByC,OAAO,eAAElF,OAAA,CAACZ,KAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBW,KAAK,EAAC;cAA+C,GAHjD,kBAAkB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIvB,CACH,CAAC;cACDS,OAAO,CAACtB,IAAI,cACV3D,OAAA,CAACb,gBAAgB;gBAEfsD,KAAK,EAAC,iBAAiB;gBACvByC,OAAO,eAAElF,OAAA,CAACZ,KAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBW,KAAK,EAAC;cAAyB,GAH3B,iBAAiB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAItB,CACH,CAAC;YACH,CAAC,MAAM,IAAIlB,YAAY,KAAK,CAAC,EAAE;cAC7B;cACA2B,OAAO,CAACtB,IAAI,cACV3D,OAAA,CAACb,gBAAgB;gBAEfsD,KAAK,EAAC,mBAAmB;gBACzByC,OAAO,eAAElF,OAAA,CAACZ,KAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBW,KAAK,EAAC;cAAiC,GAHnC,mBAAmB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIxB,CACH,CAAC;cACDS,OAAO,CAACtB,IAAI,cACV3D,OAAA,CAACb,gBAAgB;gBAEfsD,KAAK,EAAC,iBAAiB;gBACvByC,OAAO,eAAElF,OAAA,CAACZ,KAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBW,KAAK,EAAC;cAA+B,GAHjC,iBAAiB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAItB,CACH,CAAC;cACDS,OAAO,CAACtB,IAAI,cACV3D,OAAA,CAACb,gBAAgB;gBAEfsD,KAAK,EAAC,mBAAmB;gBACzByC,OAAO,eAAElF,OAAA,CAACZ,KAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBW,KAAK,EAAC;cAA6B,GAH/B,mBAAmB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIxB,CACH,CAAC;YACH;YAEA,OAAOS,OAAO;UAChB,CAAC,EAAE;QAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEdxE,OAAA,CAACvB,SAAS;QACR2G,MAAM,EAAC,OAAO;QACdhC,IAAI,EAAC,cAAc;QACnB+B,KAAK,EAAC,+BAA+B;QACrCE,SAAS;QACTlB,OAAO,EAAC,UAAU;QAClB1B,KAAK,EAAEtB,QAAQ,CAACG,YAAa;QAC7ByD,QAAQ,EAAE7B,gBAAiB;QAC3Be,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QACdoB,UAAU,EAAC;MAA2D;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eAEFxE,OAAA,CAAC1B,UAAU;QAAC6F,OAAO,EAAC,OAAO;QAACoB,KAAK,EAAC,SAAS;QAACtB,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,EACvD,CAAC,MAAM;UACN,MAAMV,YAAY,GAAGnD,YAAY,CAACmD,YAAY,IAAI,CAAC;UACnD,MAAMC,qBAAqB,GAAG,CAACD,YAAY,GAAG,CAAC,MAAM,CAAC;UACtD,MAAME,mBAAmB,GAAG,CAACF,YAAY,GAAG,CAAC,MAAM,CAAC;UAEpD,QAAQnC,QAAQ,CAACE,IAAI;YACnB,KAAK,UAAU;cACb,OAAO,0CAA0C;YACnD,KAAK,mBAAmB;cACtB,OAAO,yCAAyC;YAClD,KAAK,mBAAmB;cACtB,OAAO,wCAAwC;YACjD,KAAK,iBAAiB;cACpB,OAAO,sCAAsC;YAC/C,KAAK,UAAU;cACb,OAAOiC,YAAY,KAAK,CAAC,GAAG,mDAAmD,GAAG,uCAAuC;YAC3H,KAAK,QAAQ;cACX,OAAOA,YAAY,KAAK,CAAC,GAAG,iDAAiD,GAAG,qCAAqC;YACvH;cACE,OAAO,iDAAiD;UAC5D;QACF,CAAC,EAAE;MAAC;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGbxE,OAAA,CAAC3B,GAAG;QAAC4F,EAAE,EAAE;UAAEuB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEvB,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eAC5DhE,OAAA,CAACxB,MAAM;UACLkH,OAAO,EAAErC,sBAAuB;UAChCsC,QAAQ,EAAEpF,OAAO,IAAI,CAACJ,YAAY,IAAI,CAACgB,QAAQ,CAACE,IAAK;UACrD8C,OAAO,EAAC,WAAW;UACnBoB,KAAK,EAAC,SAAS;UACfK,IAAI,EAAC,OAAO;UAAA5B,QAAA,EAEXzD,OAAO,gBAAGP,OAAA,CAACV,gBAAgB;YAACsG,IAAI,EAAE;UAAG;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GACvC,CAAC,MAAM;YACL,QAAQrD,QAAQ,CAACE,IAAI;cACnB,KAAK,UAAU;gBACb,OAAO,kBAAkB;cAC3B,KAAK,mBAAmB;gBACtB,OAAO,mBAAmB;cAC5B,KAAK,mBAAmB;gBACtB,OAAO,mBAAmB;cAC5B,KAAK,iBAAiB;gBACpB,OAAO,iBAAiB;cAC1B,KAAK,UAAU;gBACb,OAAO,kBAAkB;cAC3B,KAAK,QAAQ;gBACX,OAAO,gBAAgB;cACzB;gBACE,OAAO,yBAAyB;YACpC;UACF,CAAC,EAAE;QAAC;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;IAAA;IAEN;IACAxE,OAAA,CAACzB,KAAK;MAAC0F,EAAE,EAAE;QAAE4B,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA9B,QAAA,gBACzBhE,OAAA,CAAC1B,UAAU;QAAC6F,OAAO,EAAC,OAAO;QAAC4B,SAAS;QAAA/B,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbxE,OAAA,CAACvB,SAAS;QACR0G,KAAK,EAAC,mBAAmB;QACzBhB,OAAO,EAAC,UAAU;QAClBkB,SAAS;QACTD,MAAM,EAAC,QAAQ;QACf3C,KAAK,EAAE5B,UAAW;QAClBkE,QAAQ,EAAE1C,YAAa;QACvB2D,WAAW,EAAC;MAAoC;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAEFxE,OAAA,CAACxB,MAAM;QACL2F,OAAO,EAAC,WAAW;QACnBoB,KAAK,EAAC,SAAS;QACfG,OAAO,EAAEnE,QAAS;QAClBoE,QAAQ,EAAEpF,OAAQ;QAClB0D,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAE4B,EAAE,EAAE;QAAE,CAAE;QAAA9B,QAAA,EAEpBzD,OAAO,gBAAGP,OAAA,CAACV,gBAAgB;UAACsG,IAAI,EAAE;QAAG;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAAG;MAAgB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,EAEVjE,OAAO,gBACNP,OAAA,CAAC3B,GAAG;QAAC4F,EAAE,EAAE;UAAEuB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEhB,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,eAC5DhE,OAAA,CAACV,gBAAgB;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACJ7D,YAAY,CAACmB,MAAM,KAAK,CAAC,gBAC3B9B,OAAA,CAAC3B,GAAG;QAAC4F,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,gBACjBhE,OAAA,CAACT,KAAK;UAAC0G,QAAQ,EAAC,MAAM;UAAChC,EAAE,EAAE;YAAE6B,EAAE,EAAE;UAAE,CAAE;UAAA9B,QAAA,EAClCnD,UAAU,GACT,qDAAqDA,UAAU,IAAI,GACnE;QAAoD;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACRxE,OAAA,CAAC1B,UAAU;UAAC6F,OAAO,EAAC,OAAO;UAACF,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAE4B,EAAE,EAAE;UAAE,CAAE;UAAA9B,QAAA,EAAC;QAElD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxE,OAAA;UAAAgE,QAAA,gBACEhE,OAAA;YAAAgE,QAAA,eACEhE,OAAA,CAAC1B,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAAAH,QAAA,EAAC;YAE5B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACLxE,OAAA;YAAAgE,QAAA,eACEhE,OAAA,CAAC1B,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAAAH,QAAA,EAAC;YAE5B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACLxE,OAAA;YAAAgE,QAAA,eACEhE,OAAA,CAAC1B,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAAAH,QAAA,EAAC;YAE5B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACLxE,OAAA,CAAC1B,UAAU;UAAC6F,OAAO,EAAC,OAAO;UAACF,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EAAC;QAE3C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxE,OAAA;UAAAgE,QAAA,gBACEhE,OAAA;YAAAgE,QAAA,eACEhE,OAAA,CAAC1B,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAAAH,QAAA,EAAC;YAE5B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACLxE,OAAA;YAAAgE,QAAA,eACEhE,OAAA,CAAC1B,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAAAH,QAAA,EAAC;YAE5B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAENxE,OAAA,CAACL,cAAc;QAACmF,SAAS,EAAEvG,KAAM;QAAC0F,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eAC9ChE,OAAA,CAACR,KAAK;UAAAwE,QAAA,gBACJhE,OAAA,CAACJ,SAAS;YAAAoE,QAAA,eACRhE,OAAA,CAACH,QAAQ;cAAAmE,QAAA,gBACPhE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BxE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BxE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpCxE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCxE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACrCxE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAY;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnCxE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZxE,OAAA,CAACP,SAAS;YAAAuE,QAAA,EACPrD,YAAY,CAACuF,GAAG,CAAErD,IAAI,iBACrB7C,OAAA,CAACH,QAAQ;cAAAmE,QAAA,gBACPhE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAEnB,IAAI,CAACC;cAAO;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCxE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAEF,uBAAuB,CAACjB,IAAI,CAACS,YAAY;cAAC;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnExE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAED,eAAe,CAAClB,IAAI,CAACS,YAAY,EAAE,UAAU;cAAC;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvExE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAED,eAAe,CAAClB,IAAI,CAACS,YAAY,EAAE,QAAQ;cAAC;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrExE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAEnB,IAAI,CAAC+B,qBAAqB,IAAI;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1DxE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAEnB,IAAI,CAACgC,mBAAmB,IAAI;cAAG;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxDxE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,eACRhE,OAAA,CAACxB,MAAM;kBACL2F,OAAO,EAAC,UAAU;kBAClByB,IAAI,EAAC,OAAO;kBACZF,OAAO,EAAEA,CAAA,KAAMzC,gBAAgB,CAACJ,IAAI,CAAE;kBAAAmB,QAAA,EACvC;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAfC3B,IAAI,CAACC,OAAO;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACjB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClE,EAAA,CAvgBIL,gBAAgB;AAAAkG,EAAA,GAAhBlG,gBAAgB;AAygBtB,eAAeA,gBAAgB;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}