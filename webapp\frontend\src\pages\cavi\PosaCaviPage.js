import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Alert,
  Grid,
  Card,
  CardContent,
  CardActions,
  Snackbar,
  Dialog
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
  Cable as CableIcon,
  Edit as EditIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Engineering as EngineeringIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useGlobalContext } from '../../context/GlobalContext';
import AdminHomeButton from '../../components/common/AdminHomeButton';
import PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';

const PosaCaviPage = () => {
  const { isImpersonating } = useAuth();
  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog } = useGlobalContext();
  const navigate = useNavigate();

  // Recupera l'ID del cantiere dal localStorage
  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);
  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;

  // Torna alla lista dei cantieri
  const handleBackToCantieri = () => {
    navigate('/dashboard/cantieri');
  };

  // Stato per le notifiche
  const [alertMessage, setAlertMessage] = useState(null);
  const [alertSeverity, setAlertSeverity] = useState('success');
  const [openSnackbar, setOpenSnackbar] = useState(false);

  // Gestisce le notifiche
  const handleSuccess = (message) => {
    setAlertMessage(message);
    setAlertSeverity('success');
    setOpenSnackbar(true);
  };

  const handleError = (message) => {
    setAlertMessage(message);
    setAlertSeverity('error');
    setOpenSnackbar(true);
  };

  // Chiude lo snackbar
  const handleCloseSnackbar = () => {
    setOpenSnackbar(false);
  };

  // Naviga alle sottopagine
  const navigateToSubpage = (path) => {
    navigate(`/dashboard/cavi/posa/${path}`);
  };

  if (!cantiereId || isNaN(cantiereId)) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 2 }}>
          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.
        </Alert>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBackToCantieri}
        >
          Torna ai Cantieri
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4">
            Posa Cavi e Collegamenti
          </Typography>
          <IconButton
            onClick={() => window.location.reload()}
            sx={{ ml: 2 }}
            color="primary"
            title="Ricarica la pagina"
          >
            <RefreshIcon />
          </IconButton>
        </Box>
        <AdminHomeButton />
      </Box>

      <Paper sx={{ mb: 3, p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            Cantiere: {cantiereName} (ID: {cantiereId})
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<ArrowBackIcon />}
            onClick={handleBackToCantieri}
          >
            Torna ai Cantieri
          </Button>
        </Box>
      </Paper>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CableIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Inserisci metri posati
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Registra i metri di cavo posati durante l'installazione. Aggiorna lo stato del cavo e la bobina associata.
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                size="small"
                color="primary"
                onClick={() => navigateToSubpage('inserisci-metri')}
              >
                Apri
              </Button>
            </CardActions>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', border: '2px solid #2e7d32' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CableIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div" color="success.main">
                  Inserisci metri posati (Semplificato)
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Versione semplificata per registrare i metri di cavo posati con un'interfaccia più diretta e intuitiva.
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                size="small"
                color="success"
                onClick={() => navigateToSubpage('metri-posati-semplificato')}
              >
                Apri
              </Button>
            </CardActions>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <EditIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Modifica cavo
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Modifica le caratteristiche di un cavo esistente nel cantiere.
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                size="small"
                color="primary"
                onClick={() => {
                  // Apre il dialogo di modifica cavi
                  setOpenModificaCavoDialog(true);
                }}
              >
                Apri
              </Button>
            </CardActions>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AddIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Aggiungi nuovo cavo
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Aggiungi un nuovo cavo al cantiere con tutte le sue caratteristiche.
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                size="small"
                color="primary"
                onClick={() => navigateToSubpage('aggiungi-cavo')}
              >
                Apri
              </Button>
            </CardActions>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <DeleteIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Elimina cavo
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Elimina un cavo dal cantiere o marcalo come SPARE se già posato.
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                size="small"
                color="primary"
                onClick={() => setOpenEliminaCavoDialog(true)}
              >
                Apri
              </Button>
            </CardActions>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <EditIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Modifica bobina cavo posato
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Modifica la bobina associata a un cavo già posato.
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                size="small"
                color="primary"
                onClick={() => navigateToSubpage('modifica-bobina')}
              >
                Apri
              </Button>
            </CardActions>
          </Card>
        </Grid>


      </Grid>

      {/* Dialogo per l'eliminazione dei cavi */}
      <Dialog
        open={openEliminaCavoDialog}
        onClose={() => setOpenEliminaCavoDialog(false)}
        fullWidth
        maxWidth="md"
      >
        <PosaCaviCollegamenti
          cantiereId={cantiereId}
          onSuccess={(message) => {
            handleSuccess(message);
            setOpenEliminaCavoDialog(false);
          }}
          onError={handleError}
          initialOption="eliminaCavo"
        />
      </Dialog>

      {/* Dialogo per la modifica dei cavi */}
      <Dialog
        open={openModificaCavoDialog}
        onClose={() => setOpenModificaCavoDialog(false)}
        fullWidth
        maxWidth="md"
      >
        <PosaCaviCollegamenti
          cantiereId={cantiereId}
          onSuccess={(message) => {
            handleSuccess(message);
            setOpenModificaCavoDialog(false);
          }}
          onError={handleError}
          initialOption="modificaCavo"
        />
      </Dialog>

      <Snackbar
        open={openSnackbar}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>
          {alertMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PosaCaviPage;
