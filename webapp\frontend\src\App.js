import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

import { useAuth } from './context/AuthContext';
import LoginPage from './pages/LoginPageNew';
import Dashboard from './pages/Dashboard';
import ProtectedRoute from './components/ProtectedRoute';

// Tema personalizzato
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#0d47a1',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#dc004e',
      light: '#ff5c8d',
      dark: '#9a0036',
      contrastText: '#ffffff',
    },
    info: {
      main: '#0288d1',
      light: '#03dac6',
      dark: '#018786',
      contrastText: '#ffffff',
    },
    success: {
      main: '#2e7d32',
      light: '#4caf50',
      dark: '#1b5e20',
      contrastText: '#ffffff',
    },
    warning: {
      main: '#ed6c02',
      light: '#ff9800',
      dark: '#e65100',
      contrastText: '#ffffff',
    },
    error: {
      main: '#d32f2f',
      light: '#f44336',
      dark: '#c62828',
      contrastText: '#ffffff',
    },
  },
});

function App() {
  const { isAuthenticated, loading, user } = useAuth();

  console.log('App - Stato autenticazione:', { isAuthenticated, loading });

  // Se l'applicazione è in caricamento, mostra un indicatore di caricamento
  if (loading) {
    console.log('App - Mostrando indicatore di caricamento');
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
          <div style={{ textAlign: 'center' }}>
            <div>Caricamento...</div>
          </div>
        </div>
      </ThemeProvider>
    );
  }

  console.log('App - Rendering principale, isAuthenticated:', isAuthenticated);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Routes>
          <Route path="/login" element={
          isAuthenticated ? (
            user?.role === 'owner' ? <Navigate to="/dashboard/admin" replace /> :
            user?.role === 'user' ? <Navigate to="/dashboard/cantieri" replace /> :
            user?.role === 'cantieri_user' ? <Navigate to="/dashboard/cavi/visualizza" replace /> :
            <Navigate to="/dashboard" replace />
          ) : <LoginPage />
        } />
        <Route
          path="/dashboard/*"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="/"
          element={
            isAuthenticated ? (
              user?.role === 'owner' ? <Navigate to="/dashboard/admin" replace /> :
              user?.role === 'user' ? <Navigate to="/dashboard/cantieri" replace /> :
              user?.role === 'cantieri_user' ? <Navigate to="/dashboard/cavi/visualizza" replace /> :
              <Navigate to="/dashboard" replace />
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />
      </Routes>
    </ThemeProvider>
  );
}

export default App;
